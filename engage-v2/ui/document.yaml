components:
  schemas:
    AMI:
      additionalProperties: false
      properties:
        creation_date:
          format: date-time
          type: string
        description:
          type: string
        image_id:
          type: string
        name:
          type: string
      required:
        - image_id
        - name
        - description
        - creation_date
      type: object
    AWSAccount:
      additionalProperties: false
      properties:
        account_cloud_id:
          type: string
        account_cloud_status:
          type: string
        account_creation_status:
          type: string
        account_name:
          type: string
        created_at:
          format: date-time
          type: string
        created_by:
          type: string
        nickname:
          type: string
      required:
        - account_name
        - account_creation_status
        - created_at
        - account_cloud_id
        - account_cloud_status
        - nickname
        - created_by
      type: object
    ActivityLog:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        message:
          type: string
        type:
          type: string
        username:
          type: string
      required:
        - message
        - type
        - username
        - created_at
      type: object
    AdminScript:
      additionalProperties: false
      properties:
        content:
          type: string
        created_at:
          format: date-time
          type: string
        description:
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        script_type:
          enum:
            - ADMIN
          type: string
        updated_at:
          format: date-time
          type: string
        user_id:
          type: string
      required:
        - id
        - name
        - description
        - content
        - script_type
        - created_at
        - updated_at
        - user_id
      type: object
    AzureAMI:
      additionalProperties: false
      properties:
        name:
          type: string
        offer:
          type: string
        publisher:
          type: string
        sku:
          type: string
        version:
          type: string
      required:
        - publisher
        - offer
        - sku
        - version
        - name
      type: object
    AzureTenant:
      additionalProperties: false
      properties:
        account_cloud_status:
          type: string
        created_at:
          format: date-time
          type: string
        creation_status:
          type: string
        secrets_saved:
          type: boolean
        subscription_id:
          type: string
        tenant_id:
          type: string
      required:
        - tenant_id
        - subscription_id
        - account_cloud_status
        - created_at
        - secrets_saved
        - creation_status
      type: object
    AzureTenantForm:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/AzureTenantForm.json
          format: uri
          readOnly: true
          type: string
        azure_app_id:
          description: Azure App ID
          maxLength: 50
          type: string
        azure_app_secret:
          description: Azure App Secret
          maxLength: 80
          type: string
        azure_subscription_id:
          description: Azure Subscription ID
          maxLength: 50
          type: string
        azure_tenant_id:
          description: Azure Tenant ID
          maxLength: 50
          type: string
      required:
        - azure_tenant_id
      type: object
    BurnDomainInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/BurnDomainInputBody.json
          format: uri
          readOnly: true
          type: string
        url:
          description: Domain URL
          examples:
            - example.com
          type: string
      required:
        - url
      type: object
    CloudAccount:
      additionalProperties: false
      properties:
        display_name:
          type: string
        id:
          type: string
        provider:
          type: string
      required:
        - id
        - display_name
        - provider
      type: object
    CloudHost:
      additionalProperties: false
      properties:
        alternative_names:
          items:
            type: string
          type:
            - array
            - "null"
        client_name:
          type: string
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        ip_addresses:
          items:
            format: ipv4
            type: string
          type:
            - array
            - "null"
        name:
          type: string
        title:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - ip_addresses
        - name
        - alternative_names
        - title
        - client_name
        - id
        - type
        - created_at
        - updated_at
      type: object
    CloudInstance:
      additionalProperties: false
      properties:
        ci_deployment_status:
          enum:
            - PENDING
            - IN-PROGRESS
            - SUCCESS
            - WARNING
            - ERROR
          type: string
        cloud_instance_id:
          type: string
        cloud_instance_state:
          enum:
            - pending
            - running
            - stopping
            - stopped
            - shutting-down
            - terminated
            - error
            - new
          type: string
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        open_ports:
          items:
            format: int32
            type: integer
          type:
            - array
            - "null"
        operating_system_image_id:
          type: string
        provider:
          type: string
        public_ipv4_address:
          format: ipv4
          type:
            - string
            - "null"
        region:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - operating_system_image_id
        - provider
        - name
        - region
        - public_ipv4_address
        - open_ports
        - ci_deployment_status
        - cloud_instance_state
        - cloud_instance_id
        - id
        - type
        - created_at
        - updated_at
      type: object
    CloudInstanceNodeGroups:
      additionalProperties: false
      properties:
        cloud_instances:
          items:
            $ref: "#/components/schemas/CloudInstance"
          type:
            - array
            - "null"
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        is_active:
          type: boolean
        name:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - id
        - name
        - is_active
        - created_at
        - updated_at
        - cloud_instances
      type: object
    CreateAWSAccountInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateAWSAccountInputBody.json
          format: uri
          readOnly: true
          type: string
        nickname:
          description: AWS Account Nickname
          type: string
      required:
        - nickname
      type: object
    CreateAdminScriptInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateAdminScriptInputBody.json
          format: uri
          readOnly: true
          type: string
        content:
          description: Content of the script
          examples:
            - This is a script content
          type: string
        description:
          description: Description of the script
          examples:
            - This is a script description
          type: string
        name:
          description: Name of the script
          examples:
            - My Script
          type: string
        script_type:
          description: Type of the script
          enum:
            - ADMIN
          examples:
            - ADMIN
          type: string
      required:
        - name
        - description
        - content
        - script_type
      type: object
    CreateAdminScriptOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateAdminScriptOutputBody.json
          format: uri
          readOnly: true
          type: string
        script:
          $ref: "#/components/schemas/AdminScript"
          description: Created admin script
      required:
        - script
      type: object
    CreateClientInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateClientInputBody.json
          format: uri
          readOnly: true
          type: string
        name:
          examples:
            - TestClient
          type: string
      required:
        - name
      type: object
    CreateEngagementInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateEngagementInputBody.json
          format: uri
          readOnly: true
          type: string
        client_name:
          type: string
        engagement_title:
          maxLength: 250
          type: string
        usernames:
          items:
            type: string
          type:
            - array
            - "null"
        wbs_code:
          examples:
            - wbs0001
          type: string
      required:
        - client_name
        - wbs_code
        - engagement_title
        - usernames
      type: object
    CreateNodeEmailAddressInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateNodeEmailAddressInputBody.json
          format: uri
          readOnly: true
          type: string
        email_address:
          examples:
            - <EMAIL>
          format: email
          type: string
        engagement_id:
          description: Engagement ID
          examples:
            - b4ccc447-46bc-465d-8526-621f1cab1c8b
          format: uuid
          type: string
        node_group_id:
          description: Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created
          examples:
            - 9a7e965f-43ed-434b-bf08-059e8dca0111
          format: uuid
          type: string
      required:
        - engagement_id
        - email_address
      type: object
    CreateNodeTypeHostInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateNodeTypeHostInputBody.json
          format: uri
          readOnly: true
          type: string
        alternative_names:
          examples:
            - - Host A1
              - Host A2
          items:
            type: string
          type:
            - array
            - "null"
        engagement_id:
          description: Engagement ID
          examples:
            - b4ccc447-46bc-465d-8526-621f1cab1c8b
          format: uuid
          type: string
        ip_addresses:
          examples:
            - - 127.0.0.1
              - ***********
          format: ipv4
          items:
            type: string
          type:
            - array
            - "null"
        name:
          examples:
            - Host A
          maxLength: 100
          type: string
        node_group_id:
          description: Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created
          examples:
            - 9a7e965f-43ed-434b-bf08-059e8dca0111
          format: uuid
          type: string
      required:
        - engagement_id
        - name
      type: object
    CreateNodeTypePersonInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateNodeTypePersonInputBody.json
          format: uri
          readOnly: true
          type: string
        company:
          examples:
            - ACME
          maxLength: 100
          type: string
        email:
          examples:
            - <EMAIL>
          format: email
          type: string
        engagement_id:
          description: Engagement ID
          examples:
            - b4ccc447-46bc-465d-8526-621f1cab1c8b
          format: uuid
          type: string
        first_name:
          examples:
            - John
          maxLength: 100
          type: string
        last_name:
          examples:
            - Doe
          maxLength: 100
          type: string
        node_group_id:
          description: Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created
          examples:
            - 9a7e965f-43ed-434b-bf08-059e8dca0111
          format: uuid
          type: string
        title:
          examples:
            - CEO
          maxLength: 100
          type: string
      required:
        - engagement_id
        - first_name
      type: object
    CreateNodeTypeUrlInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateNodeTypeUrlInputBody.json
          format: uri
          readOnly: true
          type: string
        engagement_id:
          description: Engagement ID
          examples:
            - b4ccc447-46bc-465d-8526-621f1cab1c8b
          format: uuid
          type: string
        node_group_id:
          description: Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created
          examples:
            - 9a7e965f-43ed-434b-bf08-059e8dca0111
          format: uuid
          type: string
        url:
          description: URL
          format: uri
          type: string
      required:
        - engagement_id
        - url
      type: object
    CreateNodeTypeUrlOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateNodeTypeUrlOutputBody.json
          format: uri
          readOnly: true
          type: string
        engagement_id:
          description: Engagement ID
          format: uuid
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
        url:
          type: string
      required:
        - node_id
        - engagement_id
        - url
      type: object
    CreateScriptInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateScriptInputBody.json
          format: uri
          readOnly: true
          type: string
        content:
          description: Content of the script
          examples:
            - This is a script content
          type: string
        description:
          description: Description of the script
          examples:
            - This is a script description
          type: string
        name:
          description: Name of the script
          examples:
            - My Script
          type: string
        script_type:
          description: Type of the script
          enum:
            - STANDARD
            - ADMIN
          examples:
            - ADMIN
          type: string
      required:
        - name
        - description
        - content
        - script_type
      type: object
    CreateScriptOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/CreateScriptOutputBody.json
          format: uri
          readOnly: true
          type: string
        script:
          $ref: "#/components/schemas/Script"
          description: Created User script
      required:
        - script
      type: object
    Data:
      additionalProperties: false
      properties:
        cloud_instance_state:
          type: string
        id:
          type: string
        label:
          type: string
        source:
          type: string
        target:
          type: string
      required:
        - label
        - cloud_instance_state
      type: object
    DeploymentModel:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        engagement_id:
          format: uuid
          type: string
        error_message:
          type: string
        id:
          format: uuid
          type: string
        node_id:
          format: uuid
          type: string
        node_name:
          type: string
        status:
          type: string
        terraform_module:
          type: string
        title:
          type: string
        user_id:
          format: uuid
          type: string
        username:
          type: string
        warnings:
          items:
            type: string
          type:
            - array
            - "null"
      required:
        - id
        - terraform_module
        - status
        - created_at
        - node_id
        - node_name
        - engagement_id
        - title
        - user_id
        - username
      type: object
    DomainResponse:
      additionalProperties: false
      properties:
        age:
          format: int64
          type: integer
        client:
          type: string
        created_at:
          type: string
        domain_status_enum:
          type: string
        engagement:
          type: string
        id:
          type: string
        purchase_date:
          type: string
        registrar:
          type: string
        renewal_date:
          type: string
        url:
          type: string
      required:
        - id
        - url
        - domain_status_enum
        - created_at
      type: object
    EdgeToRemove:
      additionalProperties: false
      properties:
        sourceId:
          format: uuid
          type: string
        targetId:
          format: uuid
          type: string
      required:
        - sourceId
        - targetId
      type: object
    EditAdminScriptInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditAdminScriptInputBody.json
          format: uri
          readOnly: true
          type: string
        content:
          description: Content of the script
          examples:
            - This is a script content
          type: string
        description:
          description: Updated description of the script
          examples:
            - This is an updated script description
          type: string
        name:
          description: Updated name of the script
          examples:
            - Updated Script
          type: string
        script_type:
          description: Type of the script
          enum:
            - ADMIN
          examples:
            - ADMIN
          type: string
      required:
        - name
        - description
        - content
        - script_type
      type: object
    EditAdminScriptOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditAdminScriptOutputBody.json
          format: uri
          readOnly: true
          type: string
        script:
          $ref: "#/components/schemas/AdminScript"
          description: Admin script details
      required:
        - script
      type: object
    EditDomainsInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditDomainsInputBody.json
          format: uri
          readOnly: true
          type: string
        engagement_id:
          type: string
        status:
          type: string
      required:
        - engagement_id
        - status
      type: object
    EditDomainsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditDomainsOutputBody.json
          format: uri
          readOnly: true
          type: string
        client_name:
          type: string
        engagement_id:
          type: string
        id:
          type: string
      required:
        - id
        - engagement_id
        - client_name
      type: object
    EditEngagementInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditEngagementInputBody.json
          format: uri
          readOnly: true
          type: string
        title:
          type: string
        user_ids:
          description: User IDs
          format: uuid
          items:
            type: string
          type:
            - array
            - "null"
        wbs_code:
          type: string
      required:
        - title
        - wbs_code
        - user_ids
      type: object
    EditEngagementOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditEngagementOutputBody.json
          format: uri
          readOnly: true
          type: string
        engagement:
          $ref: "#/components/schemas/Engagement"
      required:
        - engagement
      type: object
    EditNodeGroupInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeGroupInputBody.json
          format: uri
          readOnly: true
          type: string
        name:
          description: Updated Node Group Name
          type: string
      required:
        - name
      type: object
    EditNodeTypeCloudInstanceInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeCloudInstanceInputBody.json
          format: uri
          readOnly: true
          type: string
        name:
          type: string
        open_ingress_tcp_ports:
          items:
            format: int32
            type: integer
          type:
            - array
            - "null"
      required:
        - name
        - open_ingress_tcp_ports
      type: object
    EditNodeTypeCloudInstanceOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeCloudInstanceOutputBody.json
          format: uri
          readOnly: true
          type: string
        instance_type:
          description: AWS Instance type
          examples:
            - t2.micro
          type: string
        name:
          description: Instance name
          maxLength: 256
          minLength: 1
          pattern: ^[a-zA-Z0-9 _.:/=+\-@]{1,256}$
          type: string
        node_id:
          format: uuid
          type: string
        open_ingress_tcp_ports:
          description: Open ingress TCP ports
          examples:
            - - 22
          items:
            format: int32
            type: integer
          type:
            - array
            - "null"
        operating_system_image_id:
          type: string
        provider:
          enum:
            - AWS
            - GCP
            - AZURE
          type: string
        public_ipv4_address:
          format: ipv4
          type:
            - string
            - "null"
        region:
          examples:
            - eu-west-2
          type: string
      required:
        - provider
        - region
        - operating_system_image_id
        - instance_type
        - name
        - public_ipv4_address
        - open_ingress_tcp_ports
        - node_id
      type: object
    EditNodeTypeEmailAddressInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeEmailAddressInputBody.json
          format: uri
          readOnly: true
          type: string
        email_address:
          description: New email address
          format: email
          type: string
      required:
        - email_address
      type: object
    EditNodeTypeEmailAddressOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeEmailAddressOutputBody.json
          format: uri
          readOnly: true
          type: string
        email_address:
          description: Updated email address
          format: email
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
      required:
        - node_id
        - email_address
      type: object
    EditNodeTypeHostInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeHostInputBody.json
          format: uri
          readOnly: true
          type: string
        alternative_names:
          items:
            type: string
          type:
            - array
            - "null"
        ip_addresses:
          items:
            format: ipv4
            type: string
          type:
            - array
            - "null"
        name:
          type: string
      required:
        - ip_addresses
        - name
      type: object
    EditNodeTypePersonInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypePersonInputBody.json
          format: uri
          readOnly: true
          type: string
        company:
          type: string
        email:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        title:
          type: string
      required:
        - first_name
      type: object
    EditNodeTypeUrlInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeUrlInputBody.json
          format: uri
          readOnly: true
          type: string
        url:
          type: string
      required:
        - url
      type: object
    EditNodeTypeUrlOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditNodeTypeUrlOutputBody.json
          format: uri
          readOnly: true
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
        url:
          type: string
      required:
        - node_id
        - url
      type: object
    EditScriptInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditScriptInputBody.json
          format: uri
          readOnly: true
          type: string
        content:
          description: Content of the script
          examples:
            - This is a script content
          type: string
        description:
          description: Updated description of the script
          examples:
            - This is an updated script description
          type: string
        name:
          description: Updated name of the script
          examples:
            - Updated Script
          type: string
        script_type:
          description: Type of the script
          enum:
            - STANDARD
            - ADMIN
          examples:
            - ADMIN
          type: string
      required:
        - name
        - description
        - content
        - script_type
      type: object
    EditScriptOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditScriptOutputBody.json
          format: uri
          readOnly: true
          type: string
        script:
          $ref: "#/components/schemas/Script"
          description: User details
      required:
        - script
      type: object
    EditUserUsernameInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/EditUserUsernameInputBody.json
          format: uri
          readOnly: true
          type: string
        custom_username:
          description: Updated username of the user
          examples:
            - Updated User
          type: string
      required:
        - custom_username
      type: object
    Element:
      additionalProperties: false
      properties:
        classes:
          items:
            type: string
          type:
            - array
            - "null"
        data:
          $ref: "#/components/schemas/Data"
        group:
          type: string
      required:
        - group
        - data
      type: object
    EmailAddress:
      additionalProperties: false
      properties:
        client_name:
          type: string
        created_at:
          format: date-time
          type: string
        email_address:
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        title:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - email_address
        - title
        - client_name
        - id
        - type
        - name
        - created_at
        - updated_at
      type: object
    Engagement:
      additionalProperties: false
      properties:
        client_name:
          type: string
        created_at:
          format: date-time
          type: string
        error_message:
          type: string
        id:
          format: uuid
          type: string
        is_active:
          type: boolean
        node_groups:
          items:
            $ref: "#/components/schemas/NodeGroup"
          type:
            - array
            - "null"
        status:
          type: string
        title:
          type: string
        updated_at:
          format: date-time
          type: string
        users:
          items:
            $ref: "#/components/schemas/EngagementUser"
          type:
            - array
            - "null"
        wbs_code:
          type: string
      required:
        - id
        - status
        - error_message
        - title
        - client_name
        - wbs_code
        - users
        - is_active
        - created_at
        - updated_at
      type: object
    EngagementUser:
      additionalProperties: false
      properties:
        app_role:
          type: string
        custom_username:
          type: string
        full_name:
          type: string
        id:
          format: uuid
          type: string
        username:
          type: string
        valid_custom_username:
          type: boolean
        valid_ssh_key:
          type: boolean
      required:
        - id
        - full_name
        - username
        - custom_username
        - valid_custom_username
        - valid_ssh_key
        - app_role
      type: object
    ErrorDetail:
      additionalProperties: false
      properties:
        location:
          description: Where the error occurred, e.g. 'body.items[3].tags' or 'path.thing-id'
          type: string
        message:
          description: Error message text
          type: string
        value:
          description: The value at the given location
      type: object
    ErrorModel:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/ErrorModel.json
          format: uri
          readOnly: true
          type: string
        detail:
          description: A human-readable explanation specific to this occurrence of the problem.
          examples:
            - Property foo is required but is missing.
          type: string
        errors:
          description: Optional list of individual error details
          items:
            $ref: "#/components/schemas/ErrorDetail"
          type:
            - array
            - "null"
        instance:
          description: A URI reference that identifies the specific occurrence of the problem.
          examples:
            - https://example.com/error-log/abc123
          format: uri
          type: string
        status:
          description: HTTP status code
          examples:
            - 400
          format: int64
          type: integer
        title:
          description: A short, human-readable summary of the problem type. This value should not change between occurrences of the error.
          examples:
            - Bad Request
          type: string
        type:
          default: about:blank
          description: A URI reference to human-readable documentation for the error.
          examples:
            - https://example.com/errors/example
          format: uri
          type: string
      type: object
    GetAWSAccountsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetAWSAccountsOutputBody.json
          format: uri
          readOnly: true
          type: string
        accounts:
          items:
            $ref: "#/components/schemas/AWSAccount"
          type:
            - array
            - "null"
      required:
        - accounts
      type: object
    GetAdminScriptsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetAdminScriptsOutputBody.json
          format: uri
          readOnly: true
          type: string
        scripts:
          items:
            $ref: "#/components/schemas/AdminScript"
          type:
            - array
            - "null"
      required:
        - scripts
      type: object
    GetAmisOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetAmisOutputBody.json
          format: uri
          readOnly: true
          type: string
        amis:
          items:
            $ref: "#/components/schemas/AMI"
          type:
            - array
            - "null"
      required:
        - amis
      type: object
    GetAzureAmisOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetAzureAmisOutputBody.json
          format: uri
          readOnly: true
          type: string
        amis:
          items:
            $ref: "#/components/schemas/AzureAMI"
          type:
            - array
            - "null"
      required:
        - amis
      type: object
    GetAzureTenantsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetAzureTenantsOutputBody.json
          format: uri
          readOnly: true
          type: string
        tenants:
          items:
            $ref: "#/components/schemas/AzureTenant"
          type:
            - array
            - "null"
      required:
        - tenants
      type: object
    GetClientsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetClientsOutputBody.json
          format: uri
          readOnly: true
          type: string
        clients:
          items:
            type: string
          type:
            - array
            - "null"
      required:
        - clients
      type: object
    GetCloudAccountsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetCloudAccountsOutputBody.json
          format: uri
          readOnly: true
          type: string
        accounts:
          items:
            $ref: "#/components/schemas/CloudAccount"
          type:
            - array
            - "null"
      required:
        - accounts
      type: object
    GetDeploymentOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetDeploymentOutputBody.json
          format: uri
          readOnly: true
          type: string
        deployment:
          $ref: "#/components/schemas/DeploymentModel"
      required:
        - deployment
      type: object
    GetDeploymentsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetDeploymentsOutputBody.json
          format: uri
          readOnly: true
          type: string
        deployments:
          items:
            $ref: "#/components/schemas/DeploymentModel"
          type:
            - array
            - "null"
      required:
        - deployments
      type: object
    GetDomainsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetDomainsOutputBody.json
          format: uri
          readOnly: true
          type: string
        domains:
          items:
            $ref: "#/components/schemas/DomainResponse"
          type:
            - array
            - "null"
      required:
        - domains
      type: object
    GetEngagementCloudInstancesOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetEngagementCloudInstancesOutputBody.json
          format: uri
          readOnly: true
          type: string
        cloud_instance_node_groups:
          items:
            $ref: "#/components/schemas/CloudInstanceNodeGroups"
          type:
            - array
            - "null"
      required:
        - cloud_instance_node_groups
      type: object
    GetEngagementGraphOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetEngagementGraphOutputBody.json
          format: uri
          readOnly: true
          type: string
        graph:
          $ref: "#/components/schemas/Graph"
      required:
        - graph
      type: object
    GetEngagementGraphsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetEngagementGraphsOutputBody.json
          format: uri
          readOnly: true
          type: string
        graphs:
          items:
            $ref: "#/components/schemas/Graph"
          type:
            - array
            - "null"
      required:
        - graphs
      type: object
    GetEngagementOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetEngagementOutputBody.json
          format: uri
          readOnly: true
          type: string
        engagement:
          $ref: "#/components/schemas/Engagement"
      required:
        - engagement
      type: object
    GetEngagementsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetEngagementsOutputBody.json
          format: uri
          readOnly: true
          type: string
        engagements:
          items:
            $ref: "#/components/schemas/Engagement"
          type:
            - array
            - "null"
      required:
        - engagements
      type: object
    GetInstanceTypesDBOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInstanceTypesDBOutputBody.json
          format: uri
          readOnly: true
          type: string
        instance_types:
          items:
            $ref: "#/components/schemas/Item"
          type:
            - array
            - "null"
      required:
        - instance_types
      type: object
    GetInstanceTypesOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInstanceTypesOutputBody.json
          format: uri
          readOnly: true
          type: string
        instance_types:
          items:
            $ref: "#/components/schemas/InstanceType"
          type:
            - array
            - "null"
        mappings:
          items:
            $ref: "#/components/schemas/InstanceSizeMapping"
          type:
            - array
            - "null"
        validation:
          items:
            $ref: "#/components/schemas/RegionInstanceTypeValidation"
          type:
            - array
            - "null"
      required:
        - instance_types
        - validation
        - mappings
      type: object
    GetInventoryCloudInstancesOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryCloudInstancesOutputBody.json
          format: uri
          readOnly: true
          type: string
        cloud_instances:
          items:
            $ref: "#/components/schemas/InventoryCloudInstance"
          type:
            - array
            - "null"
      required:
        - cloud_instances
      type: object
    GetInventoryDomainsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryDomainsOutputBody.json
          format: uri
          readOnly: true
          type: string
        domains:
          items:
            $ref: "#/components/schemas/InventoryDomainResponse"
          type:
            - array
            - "null"
      required:
        - domains
      type: object
    GetInventoryEmailAddressesOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryEmailAddressesOutputBody.json
          format: uri
          readOnly: true
          type: string
        email_addresses:
          items:
            $ref: "#/components/schemas/EmailAddress"
          type:
            - array
            - "null"
      required:
        - email_addresses
      type: object
    GetInventoryHostsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryHostsOutputBody.json
          format: uri
          readOnly: true
          type: string
        hosts:
          items:
            $ref: "#/components/schemas/CloudHost"
          type:
            - array
            - "null"
      required:
        - hosts
      type: object
    GetInventoryOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryOutputBody.json
          format: uri
          readOnly: true
          type: string
        node_groups:
          items:
            $ref: "#/components/schemas/NodeEngagementGroup"
          type:
            - array
            - "null"
        node_types:
          items:
            $ref: "#/components/schemas/NodeType"
          type:
            - array
            - "null"
        total_nodes:
          format: int64
          type: integer
      required:
        - node_types
        - total_nodes
        - node_groups
      type: object
    GetInventoryPersonsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryPersonsOutputBody.json
          format: uri
          readOnly: true
          type: string
        persons:
          items:
            $ref: "#/components/schemas/Person"
          type:
            - array
            - "null"
      required:
        - persons
      type: object
    GetInventoryUrlsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetInventoryUrlsOutputBody.json
          format: uri
          readOnly: true
          type: string
        urls:
          items:
            $ref: "#/components/schemas/Url"
          type:
            - array
            - "null"
      required:
        - urls
      type: object
    GetNodeCloudInstanceOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetNodeCloudInstanceOutputBody.json
          format: uri
          readOnly: true
          type: string
        activity_logs:
          description: Activity Logs
          items:
            $ref: "#/components/schemas/ActivityLog"
          type:
            - array
            - "null"
        node:
          $ref: "#/components/schemas/NodeCloudInstance"
      required:
        - node
      type: object
    GetNodeTypeEmailAddressOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetNodeTypeEmailAddressOutputBody.json
          format: uri
          readOnly: true
          type: string
        activity_logs:
          description: Activity Logs
          items:
            $ref: "#/components/schemas/ActivityLog"
          type:
            - array
            - "null"
        node:
          $ref: "#/components/schemas/NodeEmailAddress"
      required:
        - node
      type: object
    GetNodeTypeHostOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetNodeTypeHostOutputBody.json
          format: uri
          readOnly: true
          type: string
        activity_logs:
          description: Activity Logs
          items:
            $ref: "#/components/schemas/ActivityLog"
          type:
            - array
            - "null"
        node:
          $ref: "#/components/schemas/NodeHost"
      required:
        - node
      type: object
    GetNodeTypePersonOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetNodeTypePersonOutputBody.json
          format: uri
          readOnly: true
          type: string
        activity_logs:
          description: Activity Logs
          items:
            $ref: "#/components/schemas/ActivityLog"
          type:
            - array
            - "null"
        node:
          $ref: "#/components/schemas/NodePerson"
      required:
        - node
      type: object
    GetNodeTypeUrlOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetNodeTypeUrlOutputBody.json
          format: uri
          readOnly: true
          type: string
        activity_logs:
          description: Activity Logs
          items:
            $ref: "#/components/schemas/ActivityLog"
          type:
            - array
            - "null"
        node:
          $ref: "#/components/schemas/NodeUrl"
      required:
        - node
      type: object
    GetRegionsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetRegionsOutputBody.json
          format: uri
          readOnly: true
          type: string
        prioritizedRegions:
          items:
            type: string
          type:
            - array
            - "null"
        regions:
          items:
            type: string
          type:
            - array
            - "null"
      required:
        - regions
        - prioritizedRegions
      type: object
    GetScriptsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetScriptsOutputBody.json
          format: uri
          readOnly: true
          type: string
        scripts:
          items:
            $ref: "#/components/schemas/Script"
          type:
            - array
            - "null"
      required:
        - scripts
      type: object
    GetUniqueRegistrarsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUniqueRegistrarsOutputBody.json
          format: uri
          readOnly: true
          type: string
        registrars:
          items:
            type: string
          type:
            - array
            - "null"
      required:
        - registrars
      type: object
    GetUserAssignmentLogsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUserAssignmentLogsOutputBody.json
          format: uri
          readOnly: true
          type: string
        userAssignmentLogs:
          items:
            $ref: "#/components/schemas/UserLogsAssignment"
          type:
            - array
            - "null"
      required:
        - userAssignmentLogs
      type: object
    GetUserAssignmentsLogsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUserAssignmentsLogsOutputBody.json
          format: uri
          readOnly: true
          type: string
        userAssignmentsLogs:
          items:
            $ref: "#/components/schemas/UserLogsAssignment"
          type:
            - array
            - "null"
      required:
        - userAssignmentsLogs
      type: object
    GetUserDetailsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUserDetailsOutputBody.json
          format: uri
          readOnly: true
          type: string
        user:
          $ref: "#/components/schemas/User"
      required:
        - user
      type: object
    GetUserEngagementTitlesOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUserEngagementTitlesOutputBody.json
          format: uri
          readOnly: true
          type: string
        engagements:
          items:
            type: string
          type:
            - array
            - "null"
      required:
        - engagements
      type: object
    GetUserManagementOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUserManagementOutputBody.json
          format: uri
          readOnly: true
          type: string
        users:
          items:
            $ref: "#/components/schemas/GroupMember"
          type:
            - array
            - "null"
      required:
        - users
      type: object
    GetUsersOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/GetUsersOutputBody.json
          format: uri
          readOnly: true
          type: string
        users:
          items:
            $ref: "#/components/schemas/User"
          type:
            - array
            - "null"
      required:
        - users
      type: object
    Graph:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        elements:
          items:
            $ref: "#/components/schemas/Element"
          type:
            - array
            - "null"
        node_group_id:
          format: uuid
          type: string
        node_group_name:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - node_group_id
        - node_group_name
        - created_at
        - updated_at
        - elements
      type: object
    GroupMember:
      additionalProperties: false
      properties:
        accountEnabled:
          type: boolean
        first_name:
          type: string
        id:
          format: uuid
          type: string
        last_name:
          type: string
        role:
          enum:
            - standard
            - admin
          type: string
        username:
          format: email
          type: string
      required:
        - id
        - first_name
        - last_name
        - username
        - role
        - accountEnabled
      type: object
    ImportDomainsBase64InputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/ImportDomainsBase64InputBody.json
          format: uri
          readOnly: true
          type: string
        fileContent:
          type: string
        fileName:
          type: string
      required:
        - fileName
        - fileContent
      type: object
    ImportDomainsOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/ImportDomainsOutputBody.json
          format: uri
          readOnly: true
          type: string
        importedCount:
          format: int64
          type: integer
      required:
        - importedCount
      type: object
    InstanceSizeMapping:
      additionalProperties: false
      properties:
        id:
          format: int64
          type: integer
        instance_type:
          type: string
        priority:
          format: int32
          type: integer
        provider:
          type: string
        size_alias:
          type: string
      required:
        - id
        - provider
        - size_alias
        - priority
        - instance_type
      type: object
    InstanceType:
      additionalProperties: false
      properties:
        alias:
          type: string
        type:
          type: string
      required:
        - alias
        - type
      type: object
    InstanceTypeMappingChange:
      additionalProperties: false
      properties:
        action:
          type: string
        instance_type:
          type: string
        priority:
          format: int64
          type: integer
        provider:
          type: string
        size_alias:
          type: string
      required:
        - provider
        - size_alias
        - priority
        - instance_type
        - action
      type: object
    InventoryCloudInstance:
      additionalProperties: false
      properties:
        ci_deployment_status:
          enum:
            - PENDING
            - IN-PROGRESS
            - SUCCESS
            - WARNING
            - ERROR
          type: string
        client_name:
          type: string
        cloud_instance_id:
          examples:
            - i-034295fe21c3bebf7
          type: string
        cloud_instance_state:
          enum:
            - pending
            - running
            - stopping
            - stopped
            - shutting-down
            - terminated
            - error
            - new
          type: string
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        open_ports:
          items:
            format: int32
            type: integer
          type:
            - array
            - "null"
        operating_system_image_id:
          type: string
        provider:
          type: string
        public_ipv4_address:
          format: ipv4
          type:
            - string
            - "null"
        region:
          type: string
        title:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - operating_system_image_id
        - provider
        - name
        - region
        - public_ipv4_address
        - open_ports
        - title
        - client_name
        - ci_deployment_status
        - cloud_instance_state
        - cloud_instance_id
        - id
        - type
        - created_at
        - updated_at
      type: object
    InventoryDomainResponse:
      additionalProperties: false
      properties:
        activity_count:
          format: int64
          type: integer
        age:
          format: int64
          type: integer
        client:
          type: string
        created_at:
          type: string
        engagement:
          type: string
        id:
          type: string
        last_activity:
          type: string
        message:
          type: string
        purchase_date:
          type: string
        registrar:
          type: string
        renewal_date:
          type: string
        status:
          type: string
        type:
          type: string
        url:
          type: string
        username:
          type: string
      required:
        - url
        - activity_count
      type: object
    Item:
      additionalProperties: false
      properties:
        alias:
          type: string
        type:
          type: string
      required:
        - alias
        - type
      type: object
    Node:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - id
        - type
        - name
        - created_at
        - updated_at
      type: object
    NodeCloudInstance:
      additionalProperties: false
      properties:
        ci_deployment_status:
          enum:
            - PENDING
            - IN-PROGRESS
            - SUCCESS
            - WARNING
            - ERROR
          type: string
        cloud_instance_id:
          examples:
            - i-034295fe21c3bebf7
          type: string
        cloud_instance_state:
          enum:
            - pending
            - running
            - stopping
            - stopped
            - shutting-down
            - terminated
            - error
            - new
          type: string
        instance_type:
          description: AWS Instance type
          examples:
            - t2.micro
          type: string
        name:
          description: Instance name
          maxLength: 256
          minLength: 1
          pattern: ^[a-zA-Z0-9 _.:/=+\-@]{1,256}$
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
        open_ingress_tcp_ports:
          description: Open ingress TCP ports
          examples:
            - - 22
          items:
            format: int32
            type: integer
          type:
            - array
            - "null"
        operating_system_image_id:
          type: string
        provider:
          enum:
            - AWS
            - GCP
            - AZURE
          type: string
        public_ipv4_address:
          format: ipv4
          type:
            - string
            - "null"
        region:
          examples:
            - eu-west-2
          type: string
      required:
        - provider
        - region
        - operating_system_image_id
        - instance_type
        - name
        - public_ipv4_address
        - open_ingress_tcp_ports
        - node_id
        - ci_deployment_status
        - cloud_instance_state
        - cloud_instance_id
      type: object
    NodeCloudInstanceInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/NodeCloudInstanceInputBody.json
          format: uri
          readOnly: true
          type: string
        engagement_id:
          description: Engagement ID
          examples:
            - b4ccc447-46bc-465d-8526-621f1cab1c8b
          format: uuid
          type: string
        instance_type:
          description: AWS Instance type
          examples:
            - t2.micro
          type: string
        name:
          description: Instance name
          maxLength: 256
          minLength: 1
          pattern: ^[a-zA-Z0-9 _.:/=+\-@]{1,256}$
          type: string
        node_group_id:
          description: Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created
          examples:
            - 9a7e965f-43ed-434b-bf08-059e8dca0111
          format: uuid
          type: string
        open_ingress_tcp_ports:
          description: Open ingress TCP ports
          examples:
            - - 22
          items:
            format: int32
            type: integer
          type:
            - array
            - "null"
        operating_system_image:
          $ref: "#/components/schemas/OperatingSystemImageAzure"
        operating_system_image_id:
          type: string
        provider:
          enum:
            - AWS
            - GCP
            - AZURE
          type: string
        region:
          examples:
            - eu-west-2
          type: string
        selected_account_id:
          type: string
        startup_script:
          description: Startup script in base64 encoding
          examples:
            - c3VkbyBhcHQgdXBkYXRlIC15ICYmIHN1ZG8gYXB0IGluc3RhbGwgLXkgcHl0aG9uMyBweXRob24zLXBpcA==
          type: string
      required:
        - engagement_id
        - provider
        - region
        - instance_type
        - name
        - open_ingress_tcp_ports
        - selected_account_id
      type: object
    NodeEmailAddress:
      additionalProperties: false
      properties:
        email_address:
          description: Email address
          format: email
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
      required:
        - email_address
        - node_id
      type: object
    NodeEngagementGroup:
      additionalProperties: false
      properties:
        client_name:
          type: string
        engagement_name:
          type: string
        id:
          format: uuid
          type: string
        is_active:
          type: boolean
        name:
          type: string
        node_group_id:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - id
        - type
        - name
        - updated_at
        - engagement_name
        - is_active
        - node_group_id
        - client_name
      type: object
    NodeGroup:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        is_active:
          type: boolean
        name:
          type: string
        nodes:
          items:
            $ref: "#/components/schemas/Node"
          type:
            - array
            - "null"
        updated_at:
          format: date-time
          type: string
      required:
        - id
        - name
        - is_active
        - created_at
        - updated_at
      type: object
    NodeHost:
      additionalProperties: false
      properties:
        alternative_names:
          items:
            type: string
          type:
            - array
            - "null"
        ip_addresses:
          items:
            format: ipv4
            type: string
          type:
            - array
            - "null"
        name:
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
      required:
        - name
        - ip_addresses
        - alternative_names
        - node_id
      type: object
    NodePerson:
      additionalProperties: false
      properties:
        company:
          type: string
        email:
          type: string
        first_name:
          type: string
        last_name:
          type: string
        node_id:
          description: Node ID
          format: uuid
          type: string
        title:
          type: string
      required:
        - first_name
        - last_name
        - email
        - company
        - title
        - node_id
      type: object
    NodeRelationshipInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/NodeRelationshipInputBody.json
          format: uri
          readOnly: true
          type: string
        descendants:
          format: uuid
          items:
            type: string
          type:
            - array
            - "null"
        edgesToRemove:
          items:
            $ref: "#/components/schemas/EdgeToRemove"
          type:
            - array
            - "null"
        source_node_id:
          format: uuid
          type: string
        target_node_id:
          format: uuid
          type: string
        type:
          type: string
      required:
        - source_node_id
        - target_node_id
        - descendants
        - edgesToRemove
        - type
      type: object
    NodeType:
      additionalProperties: false
      properties:
        count:
          format: int64
          type: integer
        node_type:
          type: string
      required:
        - node_type
        - count
      type: object
    NodeUrl:
      additionalProperties: false
      properties:
        node_id:
          description: Node ID
          format: uuid
          type: string
        url:
          type: string
      required:
        - url
        - node_id
      type: object
    OperatingSystemImageAzure:
      additionalProperties: false
      properties:
        offer:
          type: string
        publisher:
          type: string
        sku:
          type: string
        version:
          type: string
      type: object
    Person:
      additionalProperties: false
      properties:
        client_name:
          type: string
        company:
          type: string
        created_at:
          format: date-time
          type: string
        email:
          type: string
        first_name:
          type: string
        id:
          format: uuid
          type: string
        last_name:
          type: string
        name:
          type: string
        title:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - first_name
        - last_name
        - email
        - company
        - title
        - client_name
        - id
        - type
        - name
        - created_at
        - updated_at
      type: object
    RegionInstanceTypeValidation:
      additionalProperties: false
      properties:
        available:
          type: boolean
        instance_type:
          type: string
        priority:
          format: int32
          type: integer
        size_alias:
          type: string
      required:
        - instance_type
        - size_alias
        - priority
        - available
      type: object
    Script:
      additionalProperties: false
      properties:
        content:
          type: string
        created_at:
          format: date-time
          type: string
        description:
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        script_type:
          enum:
            - STANDARD
            - ADMIN
          type: string
        updated_at:
          format: date-time
          type: string
        user_id:
          type: string
      required:
        - id
        - name
        - description
        - content
        - script_type
        - created_at
        - updated_at
        - user_id
      type: object
    SetInstanceTypeRequest:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/SetInstanceTypeRequest.json
          format: uri
          readOnly: true
          type: string
        changes:
          items:
            $ref: "#/components/schemas/InstanceTypeMappingChange"
          type:
            - array
            - "null"
        prioritizedRegions:
          items:
            type: string
          type:
            - array
            - "null"
      required:
        - changes
        - prioritizedRegions
      type: object
    SetSshKeyInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/SetSshKeyInputBody.json
          format: uri
          readOnly: true
          type: string
        ssh_key:
          description: Public SSH Key
          type: string
        ssh_key_label:
          description: Label for the SSH Key
          type: string
      required:
        - ssh_key
        - ssh_key_label
      type: object
    SetSshKeyOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/SetSshKeyOutputBody.json
          format: uri
          readOnly: true
          type: string
        user:
          $ref: "#/components/schemas/User"
          description: User details
      required:
        - user
      type: object
    TenantError:
      additionalProperties: false
      properties:
        message:
          type: string
        subscription_id:
          type: string
        tenant_id:
          type: string
      required:
        - message
      type: object
    UpdateDomainFieldInputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/UpdateDomainFieldInputBody.json
          format: uri
          readOnly: true
          type: string
        field:
          description: Field to update
          examples:
            - registrar
          type: string
        value:
          description: New value
          examples:
            - GoDaddy
          type: string
      required:
        - field
        - value
      type: object
    UpdateDomainFieldOutputBody:
      additionalProperties: false
      properties:
        $schema:
          description: A URL to the JSON Schema for this object.
          examples:
            - https://example.com/schemas/UpdateDomainFieldOutputBody.json
          format: uri
          readOnly: true
          type: string
        message:
          type: string
        success:
          type: boolean
      required:
        - success
        - message
      type: object
    UpdatedNodeGroup:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        engagement_id:
          description: Engagement ID
          format: uuid
          type: string
        is_active:
          type: boolean
        name:
          description: Updated Node Group Name
          type: string
        node_group_id:
          description: Node Group ID
          examples:
            - 9a7e965f-43ed-434b-bf08-059e8dca0111
          format: uuid
          type: string
        updated_at:
          format: date-time
          type: string
      required:
        - node_group_id
        - name
        - is_active
        - created_at
        - updated_at
        - engagement_id
      type: object
    Url:
      additionalProperties: false
      properties:
        client_name:
          type: string
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        name:
          type: string
        title:
          type: string
        type:
          type: string
        updated_at:
          format: date-time
          type: string
        url:
          type: string
      required:
        - url
        - title
        - client_name
        - id
        - type
        - name
        - created_at
        - updated_at
      type: object
    User:
      additionalProperties: false
      properties:
        app_role:
          type: string
        custom_username:
          type: string
        full_name:
          type: string
        id:
          format: uuid
          type: string
        ssh_key:
          type: string
        ssh_key_creation_date:
          format: date-time
          type: string
        ssh_key_label:
          type: string
        username:
          type: string
        valid_custom_username:
          type: boolean
        valid_ssh_key:
          type: boolean
      required:
        - id
        - full_name
        - username
        - custom_username
        - ssh_key
        - ssh_key_label
        - ssh_key_creation_date
        - valid_custom_username
        - valid_ssh_key
        - app_role
      type: object
    UserLogsAssignment:
      additionalProperties: false
      properties:
        created_at:
          format: date-time
          type: string
        id:
          format: uuid
          type: string
        message:
          type: string
        name:
          type: string
        node_id:
          type: string
        status:
          type: string
        type:
          type: string
        user_custom_username_used:
          type: string
        user_id:
          format: uuid
          type: string
      required:
        - id
        - message
        - type
        - status
        - user_id
        - user_custom_username_used
        - node_id
        - name
        - created_at
      type: object
  securitySchemes:
    microsoft_entra_auth:
      flows:
        authorizationCode:
          authorizationUrl: https://example.com/oauth/authorize
          scopes:
            scope1: Scope 1 description...
            scope2: Scope 2 description...
          tokenUrl: https://example.com/oauth/token
      type: oauth2
info:
  title: Engage API
  version: 2.0.0
openapi: 3.1.0
paths:
  /admin/scripts:
    get:
      operationId: get-admin-scripts
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAdminScriptsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Admin Scripts
      tags:
        - Admin, Scripts
    post:
      operationId: post-admin-script
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAdminScriptInputBody"
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateAdminScriptOutputBody"
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create Admin Script
      tags:
        - Admin
        - Scripts
  /admin/scripts/{script_id}:
    delete:
      operationId: delete-admin-script
      parameters:
        - description: Script ID
          example: abcd1234
          in: path
          name: script_id
          required: true
          schema:
            description: Script ID
            examples:
              - abcd1234
            format: uuid
            type: string
      responses:
        "204":
          description: No Content
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Delete Admin Script
      tags:
        - Admin
        - Scripts
    put:
      operationId: edit-admin-script
      parameters:
        - description: Script ID
          example: abcd1234
          in: path
          name: script_id
          required: true
          schema:
            description: Script ID
            examples:
              - abcd1234
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditAdminScriptInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditAdminScriptOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit Admin Script
      tags:
        - Admin
        - Scripts
  /client:
    post:
      operationId: create-client
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateClientInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create Client
      tags:
        - Clients
  /clients:
    get:
      operationId: get-clients
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetClientsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Clients
      tags:
        - Clients
  /deployment/{deploymentID}:
    get:
      operationId: get-deployment
      parameters:
        - description: Deployment ID
          example: 652545de-7daf-465a-a773-d499f200d1b8
          in: path
          name: deploymentID
          required: true
          schema:
            description: Deployment ID
            examples:
              - 652545de-7daf-465a-a773-d499f200d1b8
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDeploymentOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Deployment Details
      tags:
        - Deployments
        - Details
  /deployments:
    get:
      operationId: get-deployments
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDeploymentsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Deployments
      tags:
        - Deployments
  /domains:
    get:
      operationId: get-domains
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetDomainsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get all domains
      tags:
        - Domains
  /domains/registrars:
    get:
      operationId: get-unique-registrars
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUniqueRegistrarsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get unique registrar values
      tags:
        - Domains
  /domains/{domainID}:
    put:
      operationId: edit-domains
      parameters:
        - description: Domain ID
          example: 962d5baa-6d73-4caa-b343-af32a3819805
          in: path
          name: domainID
          required: true
          schema:
            description: Domain ID
            examples:
              - 962d5baa-6d73-4caa-b343-af32a3819805
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditDomainsInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditDomainsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit Domains
      tags:
        - Domains
  /domains/{domainID}/burn:
    put:
      operationId: burn-domain
      parameters:
        - description: Domain ID
          example: 962d5baa-6d73-4caa-b343-af32a3819805
          in: path
          name: domainID
          required: true
          schema:
            description: Domain ID
            examples:
              - 962d5baa-6d73-4caa-b343-af32a3819805
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/BurnDomainInputBody"
        required: true
      responses:
        "200":
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Mark Domain as Burned
      tags:
        - Domains
  /domains/{domainID}/field:
    put:
      operationId: update-domain-field
      parameters:
        - description: Domain ID
          example: 962d5baa-6d73-4caa-b343-af32a3819805
          in: path
          name: domainID
          required: true
          schema:
            description: Domain ID
            examples:
              - 962d5baa-6d73-4caa-b343-af32a3819805
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/UpdateDomainFieldInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UpdateDomainFieldOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Update a specific domain field
      tags:
        - Domains
  /engagement:
    post:
      operationId: post-create-engagement
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateEngagementInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create Engagement
      tags:
        - Engagements
  /engagements:
    get:
      operationId: get-engagements
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEngagementsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagements
      tags:
        - Engagements
  /engagements/{engagementID}:
    get:
      operationId: get-engagement
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementID
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
        - description: "Whether to include Node Groups (optional). Example: true or false."
          explode: false
          in: query
          name: node_groups
          schema:
            default: true
            description: "Whether to include Node Groups (optional). Example: true or false."
            format: bool
            type: boolean
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEngagementOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement
      tags:
        - Engagements
    put:
      operationId: edit-engagement
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementID
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditEngagementInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditEngagementOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit an Engagement
      tags:
        - Engagements
  /engagements/{engagementID}/aws-accounts:
    get:
      operationId: get-engagement-aws-accounts
      parameters:
        - in: path
          name: engagementID
          required: true
          schema:
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAWSAccountsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get AWS accounts for an engagement
      tags:
        - Engagements
        - AWS Accounts
  /engagements/{engagementID}/azure-tenants:
    get:
      operationId: get-engagement-azure-tenants
      parameters:
        - in: path
          name: engagementID
          required: true
          schema:
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAzureTenantsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Azure tenants for an engagement
      tags:
        - Engagements
        - Tenants
  /engagements/{engagementID}/cloud-accounts:
    get:
      operationId: get-engagement-cloud-accounts
      parameters:
        - in: path
          name: engagementID
          required: true
          schema:
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetCloudAccountsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get all cloud accounts (AWS + Azure) for an engagement
      tags:
        - Engagements
        - Cloud Accounts
  /engagements/{engagementID}/cloud-instances:
    get:
      operationId: get-engagement-cloud-instances
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementID
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEngagementCloudInstancesOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Cloud Instances
      tags:
        - Engagements, Cloud Instances
  /engagements/{engagementID}/graphs:
    get:
      operationId: get-engagement-graphs
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementID
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEngagementGraphsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Graphs
      tags:
        - Engagements, Graphs
  /engagements/{engagementID}/graphs/{nodeGroupID}:
    get:
      operationId: get-engagement-graph
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementID
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
        - description: Node Group ID
          example: 9a7e965f-43ed-434b-bf08-059e8dca0111
          in: path
          name: nodeGroupID
          required: true
          schema:
            description: Node Group ID
            examples:
              - 9a7e965f-43ed-434b-bf08-059e8dca0111
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetEngagementGraphOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get an Engagement Graph given its Node Group ID
      tags:
        - Engagements, Graphs
  /engagements/{engagementID}/user-assignment-logs:
    get:
      operationId: get-log-assignments-engagementId
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementID
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserAssignmentLogsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get User Assignment Log / engagement
      tags:
        - User Logs Assignment
  /engagements/{engagementId}/addAWSAccount:
    post:
      operationId: post-create-aws-account
      parameters:
        - description: Engagement ID
          example: b4ccc447-46bc-465d-8526-621f1cab1c8b
          in: path
          name: engagementId
          required: true
          schema:
            description: Engagement ID
            examples:
              - b4ccc447-46bc-465d-8526-621f1cab1c8b
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateAWSAccountInputBody"
        required: true
      responses:
        "202":
          description: Accepted
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Request creation of AWS account for an engagement
      tags:
        - AWSAccounts, Engagements
  /engagements/{engagementId}/addAzureTenant:
    post:
      operationId: post-create-azure-tenant
      parameters:
        - in: path
          name: engagementId
          required: true
          schema:
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/AzureTenantForm"
        required: true
      responses:
        "202":
          description: Accepted
          headers:
            Error:
              schema:
                $ref: "#/components/schemas/TenantError"
            Message:
              schema:
                type: string
            SubscriptionID:
              schema:
                type: string
            TenantID:
              schema:
                type: string
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Add an Azure tenant to an engagement
      tags:
        - AzureTenants
        - Engagements
  /inventory:
    get:
      operationId: get-inventory
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Inventory
      tags:
        - Inventory
  /inventory/cloud-instances:
    get:
      operationId: get-inventory-cloud-instances
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryCloudInstancesOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Cloud Instances
      tags:
        - Inventory
        - Cloud Instances
  /inventory/domains:
    get:
      operationId: get-inventory-domains
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryDomainsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get all domains with activity history
      tags:
        - Inventory
        - Domains
  /inventory/domains/import:
    post:
      operationId: post-inventory-domains-import-base64
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ImportDomainsBase64InputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ImportDomainsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Import domains from CSV file
      tags:
        - Inventory
        - Domains
  /inventory/domains/template:
    get:
      operationId: get-inventory-domains-template
      responses:
        "200":
          content:
            application/json:
              schema:
                contentEncoding: base64
                type: string
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Download Domain Import Template CSV
      tags:
        - Inventory
        - Domains
  /inventory/email-addresses:
    get:
      operationId: get-inventory-email-addresses
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryEmailAddressesOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Email Addresses
      tags:
        - Inventory
        - Email Addresses
  /inventory/hosts:
    get:
      operationId: get-inventory-hosts
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryHostsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Hosts
      tags:
        - Inventory
        - Hosts
  /inventory/persons:
    get:
      operationId: get-inventory-persons
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryPersonsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Persons
      tags:
        - Inventory
        - Persons
  /inventory/urls:
    get:
      operationId: get-inventory-urls
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInventoryUrlsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Engagement Urls
      tags:
        - Inventory
        - Urls
  /node-groups/{nodeGroupID}:
    put:
      operationId: edit-node-group
      parameters:
        - description: Node Group ID
          example: 9a7e965f-43ed-434b-bf08-059e8dca0111
          in: path
          name: nodeGroupID
          required: true
          schema:
            description: Node Group ID
            examples:
              - 9a7e965f-43ed-434b-bf08-059e8dca0111
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditNodeGroupInputBody"
        required: true
      responses:
        "200":
          description: OK
          headers:
            NodeGroup:
              schema:
                $ref: "#/components/schemas/UpdatedNodeGroup"
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit Node Group
      tags:
        - Node Groups
  /nodes/cloud_instance/azure-instance-types/{region}:
    get:
      operationId: get-azure-instance-types
      parameters:
        - description: Azure Region
          example: eastus
          in: path
          name: region
          required: true
          schema:
            description: Azure Region
            examples:
              - eastus
            maxLength: 20
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInstanceTypesDBOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Azure instance types (hardcoded for now)
      tags:
        - Nodes, Cloud Instance, Instances
  /nodes/cloud_instance/instance-types/{region}/{amiId}:
    get:
      operationId: get-instance-types
      parameters:
        - description: AWS Region
          example: eu-west-2
          in: path
          name: region
          required: true
          schema:
            description: AWS Region
            examples:
              - eu-west-2
            maxLength: 20
            type: string
        - description: AMI ID to filter compatible instance types
          example: ami-0123456789abcdef0
          in: path
          name: amiId
          required: true
          schema:
            description: AMI ID to filter compatible instance types
            examples:
              - ami-0123456789abcdef0
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInstanceTypesDBOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get instance types from the database
      tags:
        - Nodes, Cloud Instance, Instances
  /nodes/cloud_instance/{nodeID}:
    get:
      operationId: get-nodes-cloud-instance
      parameters:
        - description: Node ID
          example: a58a3afc-c34e-440d-ba75-2045bb0c7577
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - a58a3afc-c34e-440d-ba75-2045bb0c7577
            format: uuid
            type: string
        - description: "Whether to include activity logs (optional). Example: true or false."
          explode: false
          in: query
          name: activity_logs
          schema:
            default: true
            description: "Whether to include activity logs (optional). Example: true or false."
            type: boolean
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetNodeCloudInstanceOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get a Cloud Instance Node
      tags:
        - Nodes, Cloud Instance
    put:
      operationId: edit-nodes-cloud-instance
      parameters:
        - description: Node ID
          example: a58a3afc-c34e-440d-ba75-2045bb0c7577
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - a58a3afc-c34e-440d-ba75-2045bb0c7577
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditNodeTypeCloudInstanceInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditNodeTypeCloudInstanceOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit a Cloud Instance Node
      tags:
        - Nodes, Cloud Instance
  /nodes/cloudinstance:
    post:
      operationId: post-nodes-cloud-instance
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NodeCloudInstanceInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create a Cloud Instance Node for an Engagement
      tags:
        - Nodes, Cloud Instance
  /nodes/email-address:
    post:
      operationId: post-nodes-email-address
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateNodeEmailAddressInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create an Email Address Node for an Engagement
      tags:
        - Nodes, Email Address
  /nodes/email_address/{nodeID}:
    get:
      operationId: get-nodes-email-address
      parameters:
        - description: Node ID
          example: 962d5baa-6d73-4caa-b343-af32a3819805
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 962d5baa-6d73-4caa-b343-af32a3819805
            format: uuid
            type: string
        - description: "Whether to include activity logs (optional). Example: true or false."
          explode: false
          in: query
          name: activity_logs
          schema:
            default: true
            description: "Whether to include activity logs (optional). Example: true or false."
            format: bool
            type: boolean
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetNodeTypeEmailAddressOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get an Email Address Node
      tags:
        - Nodes, Email Address
    put:
      operationId: edit-nodes-email-address
      parameters:
        - description: Node ID
          example: 962d5baa-6d73-4caa-b343-af32a3819805
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 962d5baa-6d73-4caa-b343-af32a3819805
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditNodeTypeEmailAddressInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditNodeTypeEmailAddressOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit a Email Address Node
      tags:
        - Nodes, Email Address
  /nodes/host:
    post:
      operationId: post-nodes-host
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateNodeTypeHostInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create a Host Node for an Engagement
      tags:
        - Nodes, Host
  /nodes/host/{nodeID}:
    get:
      operationId: get-nodes-host
      parameters:
        - description: Node ID
          example: 29ffb3ac-18a8-499e-ba5d-442f89c25175
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 29ffb3ac-18a8-499e-ba5d-442f89c25175
            format: uuid
            type: string
        - description: "Whether to include activity logs (optional). Example: true or false."
          explode: false
          in: query
          name: activity_logs
          schema:
            default: true
            description: "Whether to include activity logs (optional). Example: true or false."
            format: bool
            type: boolean
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetNodeTypeHostOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get a Host Node
      tags:
        - Nodes, Host
    put:
      operationId: edit-nodes-host
      parameters:
        - description: Node ID
          example: 962d5baa-6d73-4caa-b343-af32a3819805
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 962d5baa-6d73-4caa-b343-af32a3819805
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditNodeTypeHostInputBody"
        required: true
      responses:
        "200":
          description: OK
          headers:
            AlternativeNames:
              schema:
                type: string
            IpAddresses:
              schema:
                type: string
            Name:
              schema:
                type: string
            NodeID:
              schema:
                description: Node ID
                examples:
                  - 962d5baa-6d73-4caa-b343-af32a3819805
                format: uuid
                type: string
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Update a Host Node
      tags:
        - Nodes, Host
  /nodes/person:
    post:
      operationId: post-nodes-person
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateNodeTypePersonInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create an Person Node for an Engagement
      tags:
        - Nodes, Person
  /nodes/person/{nodeID}:
    get:
      operationId: get-nodes-person
      parameters:
        - description: Node ID
          example: 48955848-1ee7-4d3d-8bdf-b48e75627ffc
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 48955848-1ee7-4d3d-8bdf-b48e75627ffc
            format: uuid
            type: string
        - description: "Whether to include activity logs (optional). Example: true or false."
          explode: false
          in: query
          name: activity_logs
          schema:
            default: true
            description: "Whether to include activity logs (optional). Example: true or false."
            format: bool
            type: boolean
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetNodeTypePersonOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get a Person Node
      tags:
        - Nodes, Person
    put:
      operationId: edit-nodes-person
      parameters:
        - description: Node ID
          example: 48955848-1ee7-4d3d-8bdf-b48e75627ffc
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 48955848-1ee7-4d3d-8bdf-b48e75627ffc
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditNodeTypePersonInputBody"
        required: true
      responses:
        "200":
          description: OK
          headers:
            Company:
              schema:
                type: string
            Email:
              schema:
                type: string
            FirstName:
              schema:
                type: string
            LastName:
              schema:
                type: string
            NodeID:
              schema:
                description: Node ID
                format: uuid
                type: string
            Title:
              schema:
                type: string
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Update a Person Node for an Engagement
      tags:
        - Nodes, Person
  /nodes/relationship:
    post:
      operationId: post-nodes-relationship
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/NodeRelationshipInputBody"
        required: true
      responses:
        "201":
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create a relationship between two Nodes for an Engagement
      tags:
        - Nodes, Relationships
  /nodes/url:
    post:
      operationId: post-nodes-url
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateNodeTypeUrlInputBody"
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateNodeTypeUrlOutputBody"
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create a URL Node for an Engagement
      tags:
        - Nodes, URL
  /nodes/url/{nodeID}:
    get:
      operationId: get-nodes-url
      parameters:
        - description: Node ID
          example: 0955f7fc-bed1-4e27-a77f-8d33012b8294
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 0955f7fc-bed1-4e27-a77f-8d33012b8294
            format: uuid
            type: string
        - description: "Whether to include activity logs (optional). Example: true or false."
          explode: false
          in: query
          name: activity_logs
          schema:
            default: true
            description: "Whether to include activity logs (optional). Example: true or false."
            format: bool
            type: boolean
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetNodeTypeUrlOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get a URL Node
      tags:
        - Nodes, URL
    put:
      operationId: edit-nodes-url
      parameters:
        - description: Node ID
          example: 48955848-1ee7-4d3d-8bdf-b48e75627ffc
          in: path
          name: nodeID
          required: true
          schema:
            description: Node ID
            examples:
              - 48955848-1ee7-4d3d-8bdf-b48e75627ffc
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditNodeTypeUrlInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditNodeTypeUrlOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Update a Url Node for an Engagement
      tags:
        - Nodes, Url
  /nodes/{node_id}:
    delete:
      operationId: delete-node
      parameters:
        - in: path
          name: node_id
          required: true
          schema:
            format: uuid
            type: string
      responses:
        "204":
          description: No Content
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Delete Node
      tags:
        - Nodes
  /nodes/{sourceID}/relationships/{targetID}:
    delete:
      operationId: delete-node-relationship
      parameters:
        - in: path
          name: sourceID
          required: true
          schema:
            format: uuid
            type: string
        - in: path
          name: targetID
          required: true
          schema:
            format: uuid
            type: string
      responses:
        "204":
          description: No Content
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Delete a relationship between two Nodes for an Engagement
      tags:
        - Nodes
        - Relationships
  /providers/aws/amis/{region}:
    get:
      operationId: get-providers-aws-amis
      parameters:
        - description: AWS Region
          example: eu-west-2
          in: path
          name: region
          required: true
          schema:
            description: AWS Region
            examples:
              - eu-west-2
            maxLength: 20
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAmisOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get AWS AMIs for a region
      tags:
        - Providers, AWS, AMIs
  /providers/aws/regions:
    get:
      operationId: get-providers-aws-regions
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetRegionsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get available AWS Regions
      tags:
        - Providers, AWS, Regions
  /providers/azure/amis/{tenantID}/{region}/{instanceType}:
    get:
      operationId: get-providers-azure-amis
      parameters:
        - description: Azure Tenant ID
          in: path
          name: tenantID
          required: true
          schema:
            description: Azure Tenant ID
            maxLength: 64
            type: string
        - description: Azure Region
          example: westeurope
          in: path
          name: region
          required: true
          schema:
            description: Azure Region
            examples:
              - westeurope
            maxLength: 20
            type: string
        - description: Azure Region
          example: westeurope
          in: path
          name: instanceType
          required: true
          schema:
            description: Azure Region
            examples:
              - westeurope
            maxLength: 20
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetAzureAmisOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Azure AMIs for a region
      tags:
        - Providers
        - Azure
        - AMIs
  /providers/azure/regions:
    get:
      operationId: get-providers-azure-regions
      parameters:
        - description: Azure Tenant ID
          explode: false
          in: query
          name: tenantId
          schema:
            description: Azure Tenant ID
            type: string
        - description: Provider (e.g., azure)
          explode: false
          in: query
          name: provider
          schema:
            description: Provider (e.g., azure)
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetRegionsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get available Azure VM Regions
      tags:
        - Providers
        - Azure
        - Regions
  /security/set-instance-types:
    post:
      operationId: set-instance-type
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SetInstanceTypeRequest"
        required: true
      responses:
        "200":
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Set instance types for multiple size and priority combinations
      tags:
        - Security, Instance Types
  /security/set-instance-types/aws/instance-types/{region}:
    get:
      operationId: get-aws-instance-types
      parameters:
        - description: AWS Region
          example: eu-west-2
          in: path
          name: region
          required: true
          schema:
            description: AWS Region
            examples:
              - eu-west-2
            maxLength: 20
            type: string
        - description: Instance category type (optional)
          example: custom_alpha
          explode: false
          in: query
          name: type
          schema:
            description: Instance category type (optional)
            examples:
              - custom_alpha
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetInstanceTypesOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get AWS instance types for a region
      tags:
        - Security, Instance Types
  /security/user-assignments-logs:
    get:
      operationId: get-assignment-logs
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserAssignmentsLogsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Assignments Logs
      tags:
        - User Logs Assignment
  /users:
    get:
      operationId: get-users
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUsersOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Users
      tags:
        - Users
  /users/user-management:
    get:
      operationId: get-user-management-users
      parameters:
        - explode: false
          in: query
          name: type
          schema:
            enum:
              - standard
              - admin
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserManagementOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get User Management Users
      tags:
        - Users
  /users/{user_id}:
    get:
      operationId: get-user-details
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserDetailsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get User Details
      tags:
        - User
    put:
      operationId: edit-user-username
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditUserUsernameInputBody"
        required: true
      responses:
        "200":
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit User username
      tags:
        - User
        - Username
  /users/{user_id}/engagements:
    get:
      operationId: get-user-engagement-titles
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetUserEngagementTitlesOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get User Engagement Titles
      tags:
        - User
  /users/{user_id}/scripts:
    get:
      operationId: get-user-scripts
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/GetScriptsOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Get Scripts
      tags:
        - User, Scripts
    post:
      operationId: post-user-script
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateScriptInputBody"
        required: true
      responses:
        "201":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/CreateScriptOutputBody"
          description: Created
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Create Script
      tags:
        - User
        - Scripts
  /users/{user_id}/scripts/{script_id}:
    delete:
      operationId: delete-user-script
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
        - description: Script ID
          example: abcd1234
          in: path
          name: script_id
          required: true
          schema:
            description: Script ID
            examples:
              - abcd1234
            format: uuid
            type: string
      responses:
        "204":
          description: No Content
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Delete Script
      tags:
        - User
        - Scripts
    put:
      operationId: edit-user-script
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
        - description: Script ID
          example: abcd1234
          in: path
          name: script_id
          required: true
          schema:
            description: Script ID
            examples:
              - abcd1234
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/EditScriptInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EditScriptOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Edit Script
      tags:
        - User
        - Scripts
  /users/{user_id}/ssh-key:
    delete:
      operationId: delete-user-ssh-key
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      responses:
        "204":
          description: No Content
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Delete User SSH Key
      tags:
        - User
        - SSH Key
    put:
      operationId: set-user-ssh-key
      parameters:
        - description: User ID
          example: cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
          in: path
          name: user_id
          required: true
          schema:
            description: User ID
            examples:
              - cdfb2590-b9b4-47ec-b2ea-3463a4da44f6
            format: uuid
            type: string
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SetSshKeyInputBody"
        required: true
      responses:
        "200":
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SetSshKeyOutputBody"
          description: OK
        default:
          content:
            application/problem+json:
              schema:
                $ref: "#/components/schemas/ErrorModel"
          description: Error
      security:
        - microsoft_entra_auth:
            - scope1
      summary: Set User SSH Key
      tags:
        - User
        - SSH Key
