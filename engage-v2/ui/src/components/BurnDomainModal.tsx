import { useState } from "react";
import { DialogTitle } from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";

import { useBurnDomain, getGetInventoryDomainsQueryKey } from "../client";
import { ActionButtons, ButtonProps } from "./ActionButtons";
import Modal from "./Modal";
import ErrorMessageModal, { ErrorMessage } from "./ErrorMessageModal";

interface BurnDomainModalProps {
  isOpen: boolean;
  closeModal: () => void;
  domainId: string;
  domainName: string;
}

export default function BurnDomainModal({
  isOpen,
  closeModal,
  domainId,
  domainName,
}: BurnDomainModalProps) {
  const queryClient = useQueryClient();
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);

  const burnDomainMutation = useBurnDomain({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error marking domain as burned.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Domain has been successfully marked as burned.");
        }
        // Refresh the domains list to reflect the updated status
        queryClient.invalidateQueries({ queryKey: getGetInventoryDomainsQueryKey() });
        closeModal();
      },
    },
  });

  const primaryButton: ButtonProps = {
    label: "Mark as Burned",
    onClick: () => {
      burnDomainMutation.mutate({ domainID: domainId, data: { url: domainName} });
    },
    variant: "primary",
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
    },
    variant: "secondary",
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <Modal
        title=""
        isOpen={isOpen}
        closeModal={closeModal}
        widthClass="w-11/12 sm:w-10/12 md:w-5/12 lg:w-4/12"
      >
        <div className="flex flex-row justify-between">
          <DialogTitle
            as="h3"
            className="flex flex-row items-center justify-between space-x-12 pb-4 text-xl font-semibold text-black dark:text-slate-100"
          >
            <div>
              Confirm marking domain as burned: <br />
              <span className="mt-2 text-red-700 dark:text-red-500">
                {domainName}
              </span>
              <div className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                This action cannot be undone.
              </div>
            </div>
          </DialogTitle>
        </div>
        <div className="mt-4 flex justify-end space-x-2">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      </Modal>
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
