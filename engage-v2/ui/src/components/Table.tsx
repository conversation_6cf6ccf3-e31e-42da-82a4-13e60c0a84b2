import { RankingInfo, rankItem } from "@tanstack/match-sorter-utils";
import {
  Column,
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  FilterFn,
  SortingState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import {
  HiChevronDown,
  HiChevronLeft,
  HiChevronRight,
  HiChevronUp,
} from "react-icons/hi";
import { HiChevronUpDown, HiMagnifyingGlass, HiXMark } from "react-icons/hi2";
import { TbFilterOff } from "react-icons/tb";

import { useTheme } from "../context/ThemeProvider";

interface TableProps<T> {
  data: T[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: ColumnDef<T, any>[];
  getSubRows?: (row: T) => T[] | undefined;
  subRowLen?: number;
}

declare module "@tanstack/react-table" {
  interface FilterFns {
    fuzzy: FilterFn<unknown>;
  }
  interface FilterMeta {
    itemRank: RankingInfo;
  }
}

// Fuzzy filter function
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta({ itemRank });
  return itemRank.passed;
};
// eslint-disable-next-line @typescript-eslint/no-explicit-any
function Filter({ column }: { column: Column<any, unknown> }) {
  const columnFilterValue = column.getFilterValue();

  return (
    <DebouncedInput
      type="text"
      value={(columnFilterValue ?? "") as string}
      onChange={(value) => column.setFilterValue(value)}
      placeholder={`Search...`}
      className="mt-1 w-5/6 rounded-sm border border-gray-300 bg-transparent p-2 focus:outline-hidden focus:ring-2 focus:ring-purple-500 dark:border-slate-50"
    />
  );
}

export function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: {
  value: string | number;
  onChange: (value: string | number) => void;
  debounce?: number;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange">) {
  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => onChange(value), debounce);
    return () => clearTimeout(timeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  return (
    <input
      {...props}
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  );
}

function Table<T>({ data, columns, getSubRows, subRowLen = 0 }: TableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });
  
  const table = useReactTable<T>({
    data,
    columns,
    filterFns: { fuzzy: fuzzyFilter },
    state: { sorting, expanded, columnFilters, globalFilter, pagination },
    globalFilterFn: fuzzyFilter,
    getExpandedRowModel: getExpandedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onExpandedChange: setExpanded,
    getSubRows,
  });
  
  const { isDarkMode } = useTheme();
  
  // Function to reset all filters
  const resetAllFilters = () => {
    setGlobalFilter("");
    setColumnFilters([]);
    table.resetColumnFilters();
    table.resetGlobalFilter();
  };
  
  // Check if any filters are active
  const hasActiveFilters = globalFilter !== "" || columnFilters.length > 0;
  
  useEffect(() => {
    setPagination((prevPagination) => ({
      ...prevPagination,
      pageSize: prevPagination.pageSize + subRowLen,
    }));
  }, [subRowLen]);

  return (
    <div className="mt-2 overflow-x-auto sm:mt-4 md:p-2">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
        <div className="top-1 flex w-full flex-row items-center rounded-sm border border-solid border-gray-400 bg-white px-4 focus-within:outline-hidden focus-within:ring-2 focus-within:ring-purple-500 dark:bg-[#303c4f] md:mb-0 md:mt-3 md:max-w-sm xl:max-w-lg">
          <HiMagnifyingGlass
            className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
          />
          <DebouncedInput
            value={globalFilter ?? ""}
            onChange={(value) => setGlobalFilter(String(value))}
            className="font-lg w-full bg-transparent p-2 focus:outline-hidden focus:ring-0 dark:text-white"
            placeholder="Search all columns..."
          />
          {globalFilter && (
            <button 
              onClick={() => setGlobalFilter("")}
              className="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-white"
            >
              <HiXMark className="h-5 w-5" />
            </button>
          )}
        </div>
        
        {hasActiveFilters && (
          <button
            onClick={resetAllFilters}
            className="mt-2 md:mt-0 flex items-center space-x-1 px-3 py-2 rounded-md border border-gray-300 hover:bg-gray-100 dark:border-gray-600 dark:hover:bg-gray-700 dark:text-white transition-colors"
            title="Reset all filters"
          >
            <TbFilterOff className="h-4 w-4" />
            <span>Reset Filters</span>
          </button>
        )}
      </div>
      <div className="relative overflow-x-auto w-full mt-4 border border-gray-100 rounded-md dark:border-gray-700">
        <table className="w-full table-auto min-w-[640px]">
          <thead className="sticky top-0 z-10 bg-white dark:bg-[#303c4f] dark:text-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="pb-2 pt-3 px-3 text-left text-sm font-medium dark:text-white"
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        className={
                          header.column.getCanSort()
                            ? "flex cursor-pointer items-center justify-between"
                            : ""
                        }
                        onClick={header.column.getToggleSortingHandler()}
                        title={
                          header.column.getCanSort()
                            ? header.column.getIsSorted() === "asc"
                              ? "Sort ascending"
                              : header.column.getIsSorted() === "desc"
                                ? "Sort descending"
                                : "Clear sort"
                            : undefined
                        }
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                        {header.column.getCanSort() &&
                          {
                            asc: <HiChevronUp className="h-4 w-4" />,
                            desc: <HiChevronDown className="h-4 w-4" />,
                            clear: (
                              <HiChevronUpDown
                                className={`${isDarkMode ? "text-white" : "text-black"} h-4 w-4`}
                              />
                            ),
                          }[header.column.getIsSorted() || "clear"]}
                      </div>
                    )}
                    {header.column.getCanFilter() && (
                      <Filter column={header.column} />
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="dark:text-white">
            {table.getRowModel().rows.map((row, rowIndex) => {
              // Check if any column has a getRowClassName function in its meta
              const rowBackgroundColumn = columns.find(col => 
                col.meta && typeof col.meta.getRowClassName === 'function'
              );
              
              // Get the background color class if the function exists
              const customBgClass = rowBackgroundColumn?.meta?.getRowClassName?.(row) || '';
              
              // Use the custom background class if available, otherwise use the default striped pattern
              const bgClass = customBgClass || 
                (rowIndex % 2 === 1 ? "bg-slate-50 dark:bg-slate-600" : "");
              
              return (
                <tr
                  key={row.id}
                  className={bgClass}
                >
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className="py-2 px-3 text-left text-sm font-normal"
                    >
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <div className="flex flex-wrap justify-between items-center pt-4 gap-2">
        <div className="flex gap-2">
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <HiChevronLeft
              className={`${isDarkMode ? "text-white" : "text-black"} ${!table.getCanPreviousPage() ? "text-gray-400" : "text-black dark:text-white"} h-5 w-5 cursor-pointer`}
            />
          </button>
          <div className="rounded-sm bg-purple-700 px-3 py-2 text-white dark:bg-purple-500">
            {table.getState().pagination.pageIndex + 1}
          </div>
          <button
            className={
              !table.getCanNextPage() ? "text-gray-100" : "text-grey-500"
            }
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <HiChevronRight
              className={`${isDarkMode ? "text-white" : "text-black"} ${!table.getCanNextPage() ? "text-gray-400" : "text-black dark:text-white"} h-5 w-5 cursor-pointer`}
            />
          </button>
        </div>
        <div className="align-center">
          <span className="content-bottom text-xs font-medium text-gray-400 dark:text-gray-200">
            Showing {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()} pages
          </span>
        </div>
      </div>
    </div>
  );
}

export default Table;
