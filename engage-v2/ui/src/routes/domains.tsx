import { createFileRoute } from "@tanstack/react-router";
import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-toastify";



import {
  getGetInventoryDomainsQueryKey,
  usePostNodesUrl,
  useEditDomains,
  useGetDomains,
  useGetUniqueRegistrars,
  useUpdateDomainField,
} from "../client";
import type { CreateNodeTypeUrlOutputBody, DomainResponse } from "../model";
import BreadCrumbs from "../components/BreadCrumbs";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import Header from "../components/Header";
import { inventoryDomainColumns } from "../components/InventoryDomainColumn";
import ResponsiveSideNav from "../components/ResponsiveSideNav";
import Table from "../components/Table";
import { errorCode } from "../utils/assets";
import { ExcelImporter } from "../components/ExcelImporter";
import AssignDomainModal from "../components/AssignDomainModal";
import ConfirmationModal from "../components/ConfirmationModal";



export const Route = createFileRoute("/domains")({
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
  component: Domains,
});

function Domains() {
  const queryClient = useQueryClient();
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const [isAssignModalOpen, setIsAssignModalOpen] = useState<boolean>(false);
  const [selectedDomain, setSelectedDomain] = useState<DomainResponse | null>(null);

  // Confirmation modal state
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState<boolean>(false);
  const [confirmModalConfig, setConfirmModalConfig] = useState<{
    title: string;
    message: string;
    confirmButtonText: string;
    confirmButtonVariant: "primary" | "danger";
    domainName: string;
    onConfirm: () => void;
  } | null>(null);

  
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  // Get domains data
  const { data: domainsList } = useGetDomains({
    query: {
      queryKey: getGetInventoryDomainsQueryKey(),
    },
  });

  // Get unique registrars for dropdown
  const { data: registrarsResponse } = useGetUniqueRegistrars({
    query: {
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  });
  const registrarsData = registrarsResponse?.registrars || [];

  // Before rendering the table, add this debugging
  console.log("Original domains data:", domainsList?.domains);

  // Helper function to format dates from various formats to DD.MM.YYYY
  const formatDateForDisplay = (dateString: string): string => {
    if (!dateString || dateString.trim() === "") return "";

    console.log("formatDateForDisplay input:", dateString);

    try {
      let date: Date;

      // Handle DD.MM.YYYY format (already correct)
      if (dateString.includes('.') && dateString.split('.').length === 3) {
        const parts = dateString.split('.');
        if (parts.length === 3 && parts[2].length === 4) {
          console.log("Date already in DD.MM.YYYY format:", dateString);
          return dateString;
        }
      }

      // Handle ISO format (2025-04-30T11:20:25.393Z)
      if (dateString.includes('T') || dateString.includes('Z')) {
        console.log("Parsing ISO format:", dateString);
        date = new Date(dateString);
      }
      // Handle YYYY-MM-DD format
      else if (dateString.includes('-') && dateString.split('-').length === 3) {
        const parts = dateString.split('-');
        if (parts[0].length === 4) {
          console.log("Parsing YYYY-MM-DD format:", dateString);
          date = new Date(dateString);
        } else {
          console.log("Unknown dash format:", dateString);
          return dateString; // Already in some other format
        }
      }
      else {
        console.log("Unknown date format:", dateString);
        return dateString; // Unknown format, return as-is
      }

      // Check if date is valid
      if (isNaN(date.getTime())) {
        console.warn("Invalid date:", dateString);
        return dateString; // Invalid date, return original
      }

      // Format to DD.MM.YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      const formatted = `${day}.${month}.${year}`;
      console.log("Formatted date:", dateString, "->", formatted);
      return formatted;
    } catch (error) {
      console.warn("Error formatting date:", dateString, error);
      return dateString; // Return original on error
    }
  };

  // Transform API domains to match the expected structure
  const domains = domainsList?.domains?.map(domain => {

    const formattedPurchaseDate = formatDateForDisplay(domain.purchase_date || "");
    const formattedRenewalDate = formatDateForDisplay(domain.renewal_date || "");


    // Create a properly structured domain object
    const transformedDomain = {
      id: domain.id,
      url: domain.url,
      registrar: domain.registrar || "",
      purchase_date: formattedPurchaseDate,
      renewal_date: formattedRenewalDate,
      domain_status_enum: domain.domain_status_enum || "UNASSIGNED",
      engagement: domain.engagement || "",
      client: domain.client || "",
      age: domain.age || 0,
      created_at: domain.created_at || ""
    };

    console.log("Transformed domain:", transformedDomain);
    return transformedDomain;
  }) || [];

  console.log("Final domains data for table:", domains);

  const openAssignModal = (domain: DomainResponse) => {
    setSelectedDomain(domain);
    setIsAssignModalOpen(true);
  };

  const closeAssignModal = () => {
    setSelectedDomain(null);
    setIsAssignModalOpen(false);
  };

  // Confirmation modal helpers
  const openConfirmModal = (config: {
    title: string;
    message: string;
    confirmButtonText: string;
    confirmButtonVariant: "primary" | "danger";
    domainName: string;
    onConfirm: () => void;
  }) => {
    setConfirmModalConfig(config);
    setIsConfirmModalOpen(true);
  };

  const closeConfirmModal = () => {
    setConfirmModalConfig(null);
    setIsConfirmModalOpen(false);
  };

  // Edit domain mutation
  const editDomainMutation = useEditDomains({
    mutation: {
      onError: (error) => {
        console.error("Error updating domain:", error);
        toast.error("Error updating domain information. Please try again later.");
      },
      onSuccess: (data) => {
        console.log("Domain updated successfully:", data);
        toast.success("Domain information updated successfully.");
        // Refresh the domains list to reflect the updated information
        queryClient.invalidateQueries({ queryKey: getGetInventoryDomainsQueryKey() });
      },
    },
  });

  // React Query mutation for domain assignment
  const assignDomainMutation = usePostNodesUrl({
    mutation: {
      onError: (error) => {
        console.error("Error assigning domain:", error);
        toast.error("Error assigning domain to engagement. Please try again later.");
      },
      onSuccess: (data: CreateNodeTypeUrlOutputBody, variables) => {
        console.log("Domain assignment successful:", data);
        toast.success("Domain has been successfully assigned to engagement.");
        
        // Find the domain by URL from the domains list
        const domainUrl = variables.data.url;
        const domainToUpdate = domains.find(d => d.url === domainUrl);
        if (domainToUpdate && data.engagement_id) {
          
          // Call the edit-domains endpoint with correct parameter structure
          editDomainMutation.mutate({
            domainID: domainToUpdate.id,
            data: {
              engagement_id: data.engagement_id,
              status: status
            }
          });
        } else {
          // Still refresh the domains list even if we can't update the domain
          queryClient.invalidateQueries({ queryKey: getGetInventoryDomainsQueryKey() });
        }
      },
      onSettled: () => {
        // Close the modal regardless of success or failure
        closeAssignModal();
      },
    },
  });

  // Update domain field mutation
  const updateDomainFieldMutation = useUpdateDomainField({
    mutation: {
      onMutate: async (variables) => {
        // Cancel any outgoing refetches (so they don't overwrite our optimistic update)
        await queryClient.cancelQueries({ queryKey: getGetInventoryDomainsQueryKey() });

        // Snapshot the previous value
        const previousDomains = queryClient.getQueryData(getGetInventoryDomainsQueryKey());

        // Optimistically update the cache
        queryClient.setQueryData(getGetInventoryDomainsQueryKey(), (old: any) => {
          if (!old?.domains) return old;

          return {
            ...old,
            domains: old.domains.map((domain: DomainResponse) => {
              if (domain.id === variables.domainID) {
                // For date fields, use the same formatting function as the initial load
                let displayValue = variables.data.value;
                if (variables.data.field === 'purchase_date' || variables.data.field === 'renewal_date') {
                  // Use the formatDateForDisplay function to ensure consistent formatting
                  displayValue = formatDateForDisplay(displayValue);
                }


                return {
                  ...domain,
                  [variables.data.field]: displayValue
                };
              }
              return domain;
            })
          };
        });

        // Return a context object with the snapshotted value
        return { previousDomains };
      },
      onError: (error, _variables, context) => {
        console.error("Error updating domain field:", error);
        toast.error("Error updating domain field. Please try again later.");

        // If the mutation fails, use the context returned from onMutate to roll back
        if (context?.previousDomains) {
          queryClient.setQueryData(getGetInventoryDomainsQueryKey(), context.previousDomains);
        }
      },
      onSuccess: (data, variables) => {
        console.log("Domain field updated successfully:", data);
        toast.success(`Domain ${variables.data.field} updated successfully.`);
        // Don't invalidate queries here - the optimistic update is sufficient
        // The data will be synced on the next natural refresh or page reload
      },
    },
  });

  const handleAssignDomain = (domain: DomainResponse, engagementId: string, nodeGroupId: string | null) => {

    // Validate required parameters (nodeGroupId can be null - backend will create new one)
    if (!domain?.url || !engagementId) {
      toast.error("Missing required information for domain assignment.");
      return;
    }

    // Call the /nodes/url endpoint to create a URL node using React Query mutation
    // If nodeGroupId is null, don't include it in the request - backend will create a new node group
    const requestData: any = {
      url: domain.url,
      engagement_id: engagementId,
    };

    if (nodeGroupId) {
      requestData.node_group_id = nodeGroupId;
    }

    assignDomainMutation.mutate({
      data: requestData,
    });
  };

  const handleBurnDomain = (domainId: string) => {
    // Find the domain name from the domains list
    const domain = domains.find(d => d.id === domainId);
    const domainName = domain?.url || 'Unknown Domain';

    // Show confirmation modal
    openConfirmModal({
      title: "Confirm marking domain as burned:",
      message: "This action cannot be undone.",
      confirmButtonText: "Mark as Burned",
      confirmButtonVariant: "danger",
      domainName,
      onConfirm: () => {
        // Use the edit endpoint to set status to BURNED
        editDomainMutation.mutate({
          domainID: domainId,
          data: {
            engagement_id: "", // Clear engagement when burning
            status: "BURNED"
          }
        });
      }
    });
  };

  // Handle domain expiration
  const handleExpireDomain = (domainId: string) => {
    // Find the domain name from the domains list
    const domain = domains.find(d => d.id === domainId);
    const domainName = domain?.url || 'Unknown Domain';

    // Show confirmation modal
    openConfirmModal({
      title: "Confirm marking domain as expired:",
      message: "This will change the domain status to expired.",
      confirmButtonText: "Mark as Expired",
      confirmButtonVariant: "primary",
      domainName,
      onConfirm: () => {
        // Use the edit endpoint to set status to EXPIRED
        editDomainMutation.mutate({
          domainID: domainId,
          data: {
            engagement_id: "", // Clear engagement when expiring
            status: "EXPIRED"
          }
        });
      }
    });
  };

  const handleActivateDomain = (domainId: string, _field: keyof DomainResponse, value: string) => {
    // Find the domain name from the domains list
    const domain = domains.find(d => d.id === domainId);
    const domainName = domain?.url || 'Unknown Domain';

    let title = "";
    let message = "";
    let confirmButtonText = "";

    // Determine the action based on the value
    switch (value) {
      case "UNASSIGNED":
        title = "Confirm releasing domain from quarantine:";
        message = "This will change the domain status to unassigned.";
        confirmButtonText = "Release from Quarantine";
        break;
      default:
        title = `Confirm changing domain status to ${value}:`;
        message = `This will change the domain status to ${value}.`;
        confirmButtonText = `Change to ${value}`;
    }

    // Show confirmation modal
    openConfirmModal({
      title,
      message,
      confirmButtonText,
      confirmButtonVariant: "primary",
      domainName,
      onConfirm: () => {
        // Use the edit endpoint to set the new status
        editDomainMutation.mutate({
          domainID: domainId,
          data: {
            engagement_id: "", // Clear engagement when changing status
            status: value
          }
        });
      }
    });
  };

  // Handle inline domain updates
  const handleDomainUpdate = (domainId: string, field: keyof DomainResponse, value: string) => {
    console.log("Domain update requested:", { domainId, field, value });

    // Find the current domain to check if value actually changed
    const currentDomain = domains.find(d => d.id === domainId);
    if (!currentDomain) {
      return;
    }

    // Get the current value for comparison
    const currentValue = currentDomain[field];

    // Check if the value actually changed
    if (currentValue === value || ((!currentValue || currentValue === '') && (!value || value === ''))) {
      console.log("No changes detected, skipping API call");
      return;
    }

    // Convert date format if needed (from DD.MM.YYYY to YYYY-MM-DD for API)
    let apiValue = value;
    if (field === 'purchase_date' || field === 'renewal_date') {
      if (value && value.includes('.')) {
        const parts = value.split('.');
        if (parts.length === 3) {
          apiValue = `${parts[2]}-${parts[1].padStart(2, '0')}-${parts[0].padStart(2, '0')}`;
        }
      }
    }

    console.log('handleDomainUpdate ', field, apiValue)

    // Call the update domain field API
    updateDomainFieldMutation.mutate({
      domainID: domainId,
      data: {
        field: field,
        value: apiValue
      }
    });
  };

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 dark:bg-slate-800 overflow-hidden">
          <div className="flex-shrink-0 px-3 py-6 md:px-6 lg:px-8 max-w-full">
            <BreadCrumbs />
            <div
              id="domains-page-main-title"
              className="flex flex-col space-y-3 pt-6 pb-2 w-full max-w-full"
            >
              <span className="text-3xl font-semibold text-black dark:text-white">
                Domains
              </span>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Manage your domain inventory with import, export, and assignment capabilities
              </p>
            </div>
          </div>

          <div className="flex-1 px-3 md:px-6 lg:px-8 pb-6 overflow-hidden">
            {/* Action Bar */}
            <div className="flex flex-col md:flex-row md:justify-end items-stretch md:items-center gap-3 mb-4 w-full max-w-full overflow-hidden">
              {/* <DomainStatusLegend /> */}
              <div className="flex-shrink-0 w-full md:w-auto max-w-full overflow-hidden">
                <ExcelImporter />
              </div>
            </div>

            <div className="h-full">
              <div className="rounded-xl bg-white shadow-lg border border-gray-200 dark:bg-[#374357] dark:border-gray-600 h-full flex flex-col">
                <div className="p-4 md:p-6 flex-1 overflow-hidden">
                  <div className="h-full overflow-x-auto">
                    <div className="min-h-[500px] min-w-[800px]">
                      <Table
                        data={domains}
                        columns={inventoryDomainColumns({
                          onAssign: openAssignModal,
                          onBurn: handleBurnDomain,
                          onUpdate: handleDomainUpdate,
                          onExpire: handleExpireDomain,
                          onAvailable: handleActivateDomain,
                          registrarOptions: registrarsData || [],
                        })}
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <AssignDomainModal
          isOpen={isAssignModalOpen}
          closeModal={closeAssignModal}
          domain={selectedDomain}
          onAssign={handleAssignDomain}
        />

        {confirmModalConfig && (
          <ConfirmationModal
            isOpen={isConfirmModalOpen}
            closeModal={closeConfirmModal}
            title={confirmModalConfig.title}
            message={confirmModalConfig.message}
            confirmButtonText={confirmModalConfig.confirmButtonText}
            confirmButtonVariant={confirmModalConfig.confirmButtonVariant}
            domainName={confirmModalConfig.domainName}
            onConfirm={confirmModalConfig.onConfirm}
          />
        )}

      </div>
    </div>
  );
}
