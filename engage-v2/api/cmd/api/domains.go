package main

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

// addDomainsRoutes registers all /domains routes to the application router
func addDomainsRoutes(api huma.API, a *application) {
	type EditDomainsInput struct {
		DomainID string `path:"domainID" format:"uuid" doc:"Domain ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		Body     struct {
			EngagementID string `json:"engagement_id"`
			Status       string `json:"status"`
		}
	}

	type EditDomainsOutput struct {
		Body struct {
			DomainID     string `json:"id"`
			EngagementID string `json:"engagement_id"`
			ClientName   string `json:"client_name"`
		}
	}

	type GetUniqueRegistrarsOutput struct {
		Body struct {
			Registrars []string `json:"registrars"`
		}
	}

	type UpdateDomainFieldInput struct {
		DomainID string `path:"domainID" format:"uuid" doc:"Domain ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		Body     struct {
			Field string `json:"field" doc:"Field to update" example:"registrar"`
			Value string `json:"value" doc:"New value" example:"GoDaddy"`
		}
	}

	type UpdateDomainFieldOutput struct {
		Body struct {
			Success bool   `json:"success"`
			Message string `json:"message"`
		}
	}

	// Edit domain endpoint
	huma.Register(api, huma.Operation{
		OperationID:   "edit-domains",
		Method:        http.MethodPut,
		Path:          "/domains/{domainID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit Domains",
		Tags:          []string{"Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *EditDomainsInput) (*EditDomainsOutput, error) {
		resp := &EditDomainsOutput{}
		fmt.Println("Update domain ")
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		// Convert domain ID to pgtype.UUID
		domainIDPgType, err := converters.StringToPgTypeUUID(input.DomainID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when converting domain ID")
		}

		// Initialize variables for client name (will be set if engagement is provided)
		var clientName string

		// Check if we're assigning or unassigning the domain
		if input.Body.Status == string(db.DomainStatusEnumUNASSIGNED) ||
			(input.Body.Status == "" && input.Body.EngagementID == "") {
			// If status is UNASSIGNED or both status and engagement_id are empty,
			// clear the engagement and client values
			err = a.queries.ClearDomainEngagementClientValues(ctx, db.ClearDomainEngagementClientValuesParams{
				ID: *domainIDPgType,
				Status: db.NullDomainStatusEnum{
					DomainStatusEnum: db.DomainStatusEnumUNASSIGNED,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error clearing domain engagement", "error", err.Error(), "domainID", input.DomainID)
				return nil, huma.Error500InternalServerError("Error updating domain information")
			}
		} else if input.Body.EngagementID != "" {
			// Only fetch engagement details when we have an engagement ID
			// Convert engagement ID to pgtype.UUID
			engagementIDPgType, err := converters.StringToPgTypeUUID(input.Body.EngagementID)
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong when converting engagement ID")
			}

			// Get engagement details to retrieve client name and title
			engagementRows, err := a.queries.GetEngagement(ctx, *engagementIDPgType)
			if err != nil {
				a.logger.Error("Error fetching engagement", "error", err.Error(), "engagementID", input.Body.EngagementID)
				return nil, huma.Error500InternalServerError("Error fetching engagement information")
			}

			if len(engagementRows) == 0 {
				a.logger.Error("Engagement not found", "engagementID", input.Body.EngagementID)
				return nil, huma.Error404NotFound("Engagement not found")
			}

			// Get client name and engagement title from the first row
			clientName = engagementRows[0].ClientName
			engagementTitle := engagementRows[0].EngagementTitle

			// Update the domain with the engagement title, client name, and status
			updateParams := db.UpdateDomainEngagementClientParams{
				ID:         *domainIDPgType,
				Engagement: pgtype.Text{String: engagementTitle, Valid: true},
				Client:     pgtype.Text{String: clientName, Valid: true},
				Status: db.NullDomainStatusEnum{
					DomainStatusEnum: db.DomainStatusEnum(input.Body.Status),
					Valid:            input.Body.Status != "",
				},
			}

			// If status is empty, default to ASSIGNED when an engagement is provided
			if input.Body.Status == "" {
				updateParams.Status = db.NullDomainStatusEnum{
					DomainStatusEnum: db.DomainStatusEnumASSIGNED,
					Valid:            true,
				}
			}

			// Update with the provided values
			err = a.queries.UpdateDomainEngagementClient(ctx, updateParams)
			if err != nil {
				a.logger.Error("Error updating domain with engagement", "error", err.Error(), "domainID", input.DomainID)
				return nil, huma.Error500InternalServerError("Error updating domain information")
			}
		} else {
			// Handle status-only updates (like BURNED, EXPIRED, etc.) without engagement
			updateParams := db.UpdateDomainEngagementClientParams{
				ID: *domainIDPgType,
				Status: db.NullDomainStatusEnum{
					DomainStatusEnum: db.DomainStatusEnum(input.Body.Status),
					Valid:            input.Body.Status != "",
				},
				// Keep existing engagement and client values by not setting them
				Engagement: pgtype.Text{Valid: false},
				Client:     pgtype.Text{Valid: false},
			}

			err = a.queries.UpdateDomainEngagementClient(ctx, updateParams)
			if err != nil {
				a.logger.Error("Error updating domain status", "error", err.Error(), "domainID", input.DomainID)
				return nil, huma.Error500InternalServerError("Error updating domain information")
			}
		}

		// Get node associated with this domain
		nodeID, err := a.queries.GetNodeIDByDomainURL(ctx, *domainIDPgType)
		if err != nil {
			a.logger.Warn("Could not find node for domain, skipping activity log creation",
				"domainID", input.DomainID, "error", err)
			// Skip creating the activity log
		} else {
			// Map domain status to appropriate log type
			var logType db.LogsNodesTypeEnum
			switch db.DomainStatusEnum(input.Body.Status) {
			case db.DomainStatusEnumASSIGNED:
				logType = db.LogsNodesTypeEnumDOMAINASSIGNED
			case db.DomainStatusEnumBURNED:
				logType = db.LogsNodesTypeEnumDOMAINBURNED
			case db.DomainStatusEnumUNASSIGNED:
				logType = db.LogsNodesTypeEnumDOMAINUNASSIGNED
			case db.DomainStatusEnumEXPIRED:
				logType = db.LogsNodesTypeEnumDOMAINEXPIRED
			case db.DomainStatusEnumQUARANTINE:
				logType = db.LogsNodesTypeEnumDOMAINQUARANTINED
			default:
				logType = db.LogsNodesTypeEnumNODEUPDATE // Default for other statuses
			}

			// Insert activity log
			err = a.queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
				Message: fmt.Sprintf("Domain %s to client %s", db.DomainStatusEnum(input.Body.Status), clientName),
				Type:    logType,
				UserID:  *userIDPgType,
				NodeID:  nodeID, // nodeID is already a pgtype.UUID
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})

			if err != nil {
				a.logger.Error("Error storing activity log for updating domain", "error", err.Error())
				// Continue execution even if logging fails
			}
		}

		// Set response values
		resp.Body.DomainID = input.DomainID
		resp.Body.EngagementID = input.Body.EngagementID
		resp.Body.ClientName = clientName

		return resp, nil
	})

	type BurnDomainInput struct {
		DomainID string `path:"domainID" format:"uuid" doc:"Domain ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		Body     struct {
			URL string `json:"url" doc:"Domain URL" example:"example.com"`
		}
	}

	// Burn domain endpoint
	huma.Register(api, huma.Operation{
		OperationID:   "burn-domain",
		Method:        http.MethodPut,
		Path:          "/domains/{domainID}/burn",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Mark Domain as Burned",
		Tags:          []string{"Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *BurnDomainInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		// Convert domain ID to pgtype.UUID
		domainIDPgType, err := converters.StringToPgTypeUUID(input.DomainID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when converting domain ID")
		}

		// Get node associated with this domain
		nodeID, err := a.queries.GetNodeIDByDomainURL(ctx, *domainIDPgType)
		if err != nil {
			a.logger.Warn("Could not find node for domain",
				"domainID", input.DomainID, "error", err)
			// Continue without node ID, we'll skip the activity log
		} else {
			// Set domain status to BURNED
			err = a.queries.SetDomainBurned(ctx, *domainIDPgType)
			if err != nil {
				a.logger.Error("Error marking domain as burned", "error", err.Error(), "domainID", input.DomainID)
				return nil, huma.Error500InternalServerError("Error updating domain status")
			}

			timestamp := pgtype.Timestamp{
				Time:             time.Now(),
				InfinityModifier: 0,
				Valid:            true,
			}

			// Insert activity log using the nodeID directly (it's already a pgtype.UUID)
			err = activitylogs.InsertLog(a.queries, "Domain set to burned successfully", db.LogsNodesTypeEnumDOMAINBURNED, *userIDPgType, nodeID, timestamp)
			if err != nil {
				a.logger.Error("Error inserting log", "error", err.Error())
				// Continue execution even if logging fails
			}
		}

		a.logger.Info("Domain marked as burned", "domainID", input.DomainID, "url", input.Body.URL)
		return &struct{}{}, nil
	})

	type GetDomainsOutput struct {
		Body struct {
			Domains []DomainResponse `json:"domains"`
		}
	}

	// Get unique registrars endpoint
	huma.Register(api, huma.Operation{
		OperationID:   "get-unique-registrars",
		Method:        http.MethodGet,
		Path:          "/domains/registrars",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get unique registrar values",
		Tags:          []string{"Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetUniqueRegistrarsOutput, error) {
		registrars, err := a.queries.GetUniqueRegistrars(ctx)
		if err != nil {
			return nil, huma.Error500InternalServerError(fmt.Sprintf("Error fetching registrars: %v", err))
		}

		// Convert []pgtype.Text to []string
		stringRegistrars := make([]string, 0, len(registrars))
		for _, registrar := range registrars {
			if registrar.Valid && registrar.String != "" {
				stringRegistrars = append(stringRegistrars, registrar.String)
			}
		}

		resp := &GetUniqueRegistrarsOutput{}
		resp.Body.Registrars = stringRegistrars
		return resp, nil
	})

	// Update domain field endpoint
	huma.Register(api, huma.Operation{
		OperationID:   "update-domain-field",
		Method:        http.MethodPut,
		Path:          "/domains/{domainID}/field",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Update a specific domain field",
		Tags:          []string{"Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *UpdateDomainFieldInput) (*UpdateDomainFieldOutput, error) {
		// Convert domain ID to UUID
		domainIDPgType, err := converters.StringToPgTypeUUID(input.DomainID)
		fmt.Println("domainIDPgType ", domainIDPgType)
		if err != nil {
			return nil, huma.Error400BadRequest("Invalid domain ID format")
		}

		// Validate field name
		validFields := map[string]bool{
			"url":           true,
			"registrar":     true,
			"purchase_date": true,
			"renewal_date":  true,
			"status":        true,
			"engagement":    true,
			"client":        true,
			"age":           true,
		}

		if !validFields[input.Body.Field] {
			return nil, huma.Error400BadRequest("Invalid field. Allowed fields: url, registrar, purchase_date, renewal_date, status, engagement, client, age")
		}

		// Use the new UpdateDomainField query that takes field name and value as parameters
		fmt.Println("UpdateDomainField ", domainIDPgType, input.Body.Field, input.Body.Value)
		_, err = a.queries.UpdateDomainField(ctx, db.UpdateDomainFieldParams{
			ID:      *domainIDPgType,
			Column2: input.Body.Field, // This is the field name parameter ($2)
			Url:     input.Body.Value, // This is the value parameter ($3)
		})
		if err != nil {
			return nil, huma.Error500InternalServerError(fmt.Sprintf("Error updating domain: %v", err))
		}

		resp := &UpdateDomainFieldOutput{}
		resp.Body.Success = true
		resp.Body.Message = fmt.Sprintf("Domain %s updated successfully", input.Body.Field)
		return resp, nil
	})

	huma.Register(api, huma.Operation{
		OperationID:   "get-domains",
		Method:        http.MethodGet,
		Path:          "/domains",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get all domains",
		Tags:          []string{"Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetDomainsOutput, error) {
		// Get all domains with their status based on node_type_urls table
		// Status will be "Active" if domain exists in node_type_urls, "Inactive" otherwise
		domains, err := a.queries.GetDomains(ctx)
		if err != nil {
			return nil, huma.Error500InternalServerError(fmt.Sprintf("Error fetching domains: %v", err))
		}

		// Convert to response format
		domainsList := make([]DomainResponse, 0, len(domains))
		for _, domain := range domains {
			// Handle purchase date - format as DD.MM.YYYY for frontend display
			var purchaseDate string
			if domain.PurchaseDate.Valid {
				purchaseDate = domain.PurchaseDate.Time.Format("02.01.2006")
			}

			// Handle renewal date - format as DD.MM.YYYY for frontend display
			var renewalDate string
			if domain.RenewalDate.Valid {
				renewalDate = domain.RenewalDate.Time.Format("02.01.2006")
			}

			// Handle domain status - use the enum value if valid, otherwise default to UNASSIGNED
			var status string
			if domain.Status.Valid {
				status = string(domain.Status.DomainStatusEnum)
			} else {
				status = string(db.DomainStatusEnumUNASSIGNED)
			}

			// Create the response with the status as a string
			domainsList = append(domainsList, DomainResponse{
				ID:           domain.ID.String(),
				URL:          domain.Url,
				Registrar:    domain.Registrar.String,
				PurchaseDate: purchaseDate,
				RenewalDate:  renewalDate,
				Status:       status,
				Engagement:   domain.Engagement.String,
				Client:       domain.Client.String,
				Age:          int(domain.Age.Int32),
				CreatedAt:    domain.CreatedAt.Time.Format("02.01.2006"),
			})
		}

		resp := &GetDomainsOutput{}
		resp.Body.Domains = domainsList
		return resp, nil
	})
}
