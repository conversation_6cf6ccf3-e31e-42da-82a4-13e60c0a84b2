package main

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

func addNodeTypeUrlRoutes(api huma.API, a *application) {
	// Get Node Type URL
	type GetNodeTypeUrlInput struct {
		NodeID              string `path:"nodeID" format:"uuid" doc:"Node ID" example:"0955f7fc-bed1-4e27-a77f-8d33012b8294"`
		IncludeActivityLogs bool   `query:"activity_logs" format:"bool" doc:"Whether to include activity logs (optional). Example: true or false." default:"true"`
	}

	type NodeUrl struct {
		Url    string `json:"url"`
		NodeID string `json:"node_id" format:"uuid" doc:"Node ID"`
	}

	type GetNodeTypeUrlOutput struct {
		Body struct {
			Node         NodeUrl       `json:"node"`
			ActivityLogs []ActivityLog `json:"activity_logs,omitempty" doc:"Activity Logs"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-nodes-url",
		Method:        http.MethodGet,
		Path:          "/nodes/url/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get a URL Node",
		Tags:          []string{"Nodes, URL"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetNodeTypeUrlInput) (*GetNodeTypeUrlOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		fmt.Println("userId ", userID)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetNodeTypeUrlOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, err
		}
		node, err := a.queries.GetNodeTypeUrl(context.Background(), db.GetNodeTypeUrlParams{
			NodeID: *nodeIDPgType,
			ID:     *userIDPgType,
		})
		if err != nil {
			a.logger.Error("Error getting Node URL", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.Node.Url = node.Url
		resp.Body.Node.NodeID = i.NodeID

		if i.IncludeActivityLogs {
			activityLogs := make([]ActivityLog, 0)
			activityLogsDB, err := a.queries.GetNodeActivityLogs(context.Background(), *nodeIDPgType)
			if err != nil {
				a.logger.Error("Error getting activity logs", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
				return nil, huma.Error404NotFound("Activity logs not found")
			}
			for _, activityLogDB := range activityLogsDB {
				activityLogs = append(activityLogs, ActivityLog{
					Message:   activityLogDB.Message,
					Type:      activityLogDB.Type,
					Username:  activityLogDB.Username,
					CreatedAt: activityLogDB.CreatedAt.Time,
				})
			}
			resp.Body.ActivityLogs = activityLogs
		}
		return resp, nil
	})

	// Edit Node Type URL
	type EditNodeTypeUrlInput struct {
		NodeID string `path:"nodeID" format:"uuid" doc:"Node ID" example:"48955848-1ee7-4d3d-8bdf-b48e75627ffc"`
		Body   struct {
			Url string `json:"url"`
		}
	}

	type EditNodeTypeUrlOutput struct {
		Body struct {
			NodeID string `json:"node_id" format:"uuid" doc:"Node ID"`
			Url    string `json:"url"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-nodes-url",
		Method:        http.MethodPut,
		Path:          "/nodes/url/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Update a Url Node for an Engagement",
		Tags:          []string{"Nodes, Url"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeTypeUrlInput) (*EditNodeTypeUrlOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &EditNodeTypeUrlOutput{}

		updatedNodeTypeUrl, err := a.queries.UpdateNodeTypeUrl(context.Background(),
			db.UpdateNodeTypeUrlParams{
				Url:    i.Body.Url,
				NodeID: *nodeIDPgType,
			})
		if err != nil {
			a.logger.Error("Error updating Url Node in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Update the nodes table with the name
		err = a.queries.UpdateNodeName(ctx, db.UpdateNodeNameParams{
			Name: i.Body.Url,
			ID:   *nodeIDPgType,
		})
		if err != nil {
			a.logger.Error("Error updating Node name in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Updated successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		a.logger.Info("Updated Url Node successfully",
			"user_id", userID,
			"node_id", i.NodeID,
			"url", i.Body.Url,
		)

		resp.Body.NodeID = i.NodeID
		resp.Body.Url = updatedNodeTypeUrl.Url

		return resp, nil
	})

	// Create Node Type URL
	type CreateNodeTypeUrlInput struct {
		Body struct {
			EngagementID string `json:"engagement_id" format:"uuid" doc:"Engagement ID"  example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
			NodeGroupID  string `json:"node_group_id,omitempty" format:"uuid" doc:"Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
			Url          string `json:"url" doc:"URL" format:"uri"`
		}
	}

	type CreateNodeTypeUrlOutput struct {
		Body struct {
			NodeID       string `json:"node_id" format:"uuid" doc:"Node ID"`
			EngagementID string `json:"engagement_id" format:"uuid" doc:"Engagement ID"`
			Url          string `json:"url"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-url",
		Method:        http.MethodPost,
		Path:          "/nodes/url",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create a URL Node for an Engagement",
		Tags:          []string{"Nodes, URL"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *CreateNodeTypeUrlInput) (*CreateNodeTypeUrlOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.Body.EngagementID)
		if err != nil {
			a.logger.Error("Error parsing EngagementID", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Check if URL node already exists but is marked as deleted
		existingNodes, err := a.queries.GetNodesByNameAndType(ctx, db.GetNodesByNameAndTypeParams{
			Name:     i.Body.Url,
			NodeType: db.NodeTypeEnumURL,
		})

		if err != nil && err.Error() != "no rows in result set" {
			a.logger.Error("Error checking for existing URL node", "error", err.Error(),
				"user_id", userID, "url", i.Body.Url)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// If node exists and is deleted, reactivate it
		if len(existingNodes) > 0 && existingNodes[0].IsDeleted {
			a.logger.Info("Found existing deleted URL node, reactivating",
				"user_id", userID,
				"url", i.Body.Url,
				"node_id", existingNodes[0].ID)

			// Reactivate the node
			err = a.queries.ReactivateNode(ctx, existingNodes[0].ID)
			if err != nil {
				a.logger.Error("Error reactivating node", "error", err.Error(),
					"user_id", userID, "url", i.Body.Url, "node_id", existingNodes[0].ID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			// Update node group if needed
			//var nodeGroupID pgtype.UUID

			if len(i.Body.NodeGroupID) > 0 {
				nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
				if err != nil {
					a.logger.Error("Error parsing NodeGroupID", "user_id", userID, "error", err.Error())
					return nil, huma.Error500InternalServerError("Something went wrong")
				}

				// Verify node group belongs to engagement
				nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(ctx, db.GetEngagementNodeGroupParams{
					ID:           *nodeGroupIDPgType,
					EngagementID: *engagementIDPgType,
				})
				if err != nil {
					a.logger.Error("Error getting Engagement Node Group", "error", err.Error(),
						"user_id", userID, "engagement_id", i.Body.EngagementID)
					return nil, huma.Error500InternalServerError("Something went wrong")
				}

				if len(nodeGroupsDB) == 1 {
					nodeGroupID := nodeGroupsDB[0].ID

					// Update node's node group
					err = a.queries.UpdateNodeNodeGroup(ctx, db.UpdateNodeNodeGroupParams{
						ID:          existingNodes[0].ID,
						NodeGroupID: nodeGroupID,
					})
					if err != nil {
						a.logger.Error("Error updating node group", "error", err.Error(),
							"user_id", userID, "node_id", existingNodes[0].ID)
						return nil, huma.Error500InternalServerError("Something went wrong")
					}
				} else {
					a.logger.Error("Error getting Node Group associated with Engagement",
						"user_id", userID, "node_group_id", i.Body.NodeGroupID, "engagement_id", i.Body.EngagementID)
					return nil, huma.Error500InternalServerError("Something went wrong")
				}
			}

			// Log the reactivation
			timestamp := pgtype.Timestamp{
				Time:             time.Now(),
				InfinityModifier: 0,
				Valid:            true,
			}

			err = activitylogs.InsertLog(a.queries, "Domain assigned to engagement", db.LogsNodesTypeEnumDOMAINASSIGNED, *userIDPgType, existingNodes[0].ID, timestamp)
			if err != nil {
				a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			///%%
			// Set domain status to ASSIGNED if it exists
			err = a.queries.SetDomainAssigned(ctx, i.Body.Url)
			if err != nil {
				a.logger.Warn("Could not update domain status", "url", i.Body.Url, "error", err.Error())
				// Continue execution even if domain update fails
			}

			// Return the reactivated node
			nodeIDString, err := converters.PgTypeUUIDToString(existingNodes[0].ID)
			if err != nil {
				a.logger.Error("Error converting Node ID to string", "user_id", userID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			resp := &CreateNodeTypeUrlOutput{}
			resp.Body.NodeID = *nodeIDString
			resp.Body.EngagementID = i.Body.EngagementID
			resp.Body.Url = i.Body.Url

			a.logger.Info("Reactivated URL Node successfully",
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"url", i.Body.Url,
				"node_id", *nodeIDString,
			)

			return resp, nil
		}

		// If we get here, either the node doesn't exist or isn't deleted
		var createdNode db.Node
		var nodeID pgtype.UUID
		var isNewNodeCreated bool = false

		// Check if node exists but isn't deleted (we can reuse it)
		if len(existingNodes) > 0 && !existingNodes[0].IsDeleted {
			// Node exists and is active, just use it
			createdNode = existingNodes[0]
			nodeID = existingNodes[0].ID
			isNewNodeCreated = false

			a.logger.Info("Using existing URL node",
				"user_id", userID,
				"url", i.Body.Url,
				"node_id", nodeID)

			// Update node group if needed
			if len(i.Body.NodeGroupID) > 0 {
				nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
				if err != nil {
					a.logger.Error("Error parsing NodeGroupID", "user_id", userID, "error", err.Error())
					return nil, huma.Error500InternalServerError("Something went wrong")
				}

				// Verify node group belongs to engagement
				nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(ctx, db.GetEngagementNodeGroupParams{
					ID:           *nodeGroupIDPgType,
					EngagementID: *engagementIDPgType,
				})
				if err != nil {
					a.logger.Error("Error getting Engagement Node Group", "error", err.Error(),
						"user_id", userID, "engagement_id", i.Body.EngagementID)
					return nil, huma.Error500InternalServerError("Something went wrong")
				}

				if len(nodeGroupsDB) == 1 {
					// Update node's node group with the found node group ID
					err = a.queries.UpdateNodeNodeGroup(ctx, db.UpdateNodeNodeGroupParams{
						ID:          nodeID,
						NodeGroupID: nodeGroupsDB[0].ID,
					})
					if err != nil {
						a.logger.Error("Error updating node group", "error", err.Error(),
							"user_id", userID, "node_id", nodeID)
						return nil, huma.Error500InternalServerError("Something went wrong")
					}
				} else {
					a.logger.Error("Error getting Node Group associated with Engagement",
						"user_id", userID, "node_group_id", i.Body.NodeGroupID, "engagement_id", i.Body.EngagementID)
					return nil, huma.Error500InternalServerError("Something went wrong")
				}
			}

			// Set domain status to ASSIGNED if it exists
			err = a.queries.SetDomainAssigned(ctx, i.Body.Url)
			if err != nil {
				a.logger.Warn("Could not update domain status", "url", i.Body.Url, "error", err.Error())
				// Continue execution even if domain update fails
			}
		} else {
			// Create a new node
			var nodeGroupID pgtype.UUID

			// No Node Group ID was provided to create the Node in, create a new one
			if len(i.Body.NodeGroupID) == 0 {
				associatedNodeGroup, err := a.queries.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
					Name:         "New Node Group",
					IsActive:     true,
					EngagementID: *engagementIDPgType,
					CreatedAt: pgtype.Timestamp{
						Time:             time.Now(),
						InfinityModifier: 0,
						Valid:            true,
					},
					UpdatedAt: pgtype.Timestamp{
						Time:             time.Now(),
						InfinityModifier: 0,
						Valid:            true,
					},
				})
				if err != nil {
					a.logger.Error("Error creating new Node Group in database", "error", err.Error(),
						"user_id", userID,
						"engagement_id", i.Body.EngagementID,
						"node_type", "url",
						"node_group", i.Body.NodeGroupID)
					return nil, huma.Error500InternalServerError("Something went wrong")
				}
				nodeGroupID = associatedNodeGroup.ID
			} else {
				// An existing Node Group ID was provided, check if it is associated with the Engagement
				nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
				if err != nil {
					a.logger.Error("Error parsing NodeGroupID", "user_id", userID, "error", err.Error())
					return nil, huma.Error500InternalServerError("Something went wrong")
				}
				nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(context.Background(), db.GetEngagementNodeGroupParams{
					ID:           *nodeGroupIDPgType,
					EngagementID: *engagementIDPgType,
				})
				if err != nil {
					a.logger.Error("Error getting Node Groups", "user_id", userID, "error", err.Error(), "node_group_id", i.Body.NodeGroupID)
					return nil, huma.Error500InternalServerError("Something went wrong")
				}

				// If the Node Group is associated with the Engagement, use its ID
				if len(nodeGroupsDB) == 1 {
					nodeGroupID = nodeGroupsDB[0].ID
				} else {
					a.logger.Error("Error getting Node Group associated with Engagement",
						"user_id", userID,
						"node_group_id", i.Body.NodeGroupID,
						"engagement_id", i.Body.EngagementID,
						"node_type", "url")
					return nil, huma.Error500InternalServerError("Something went wrong")
				}
			}

			createdNode, err = a.queries.CreateNode(context.Background(), db.CreateNodeParams{
				NodeType:    "URL",
				Name:        i.Body.Url,
				NodeGroupID: nodeGroupID,
			})
			if err != nil {
				a.logger.Error("Error creating Node in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "url",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			nodeID = createdNode.ID
			isNewNodeCreated = true

			err = a.queries.CreateUrlNode(context.Background(), db.CreateUrlNodeParams{
				Url:    i.Body.Url,
				NodeID: nodeID,
			})
			if err != nil {
				a.logger.Error("Error creating URL Node in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "url",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		// Set domain status to ASSIGNED if it exists
		err = a.queries.SetDomainAssigned(ctx, i.Body.Url)
		if err != nil {
			a.logger.Warn("Could not update domain status", "url", i.Body.Url, "error", err.Error())
			// Continue execution even if domain update fails
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		// Log the appropriate action
		if isNewNodeCreated {
			// Created new node - log node creation first
			err = activitylogs.InsertLog(a.queries, "URL node created", db.LogsNodesTypeEnumNODECREATION, *userIDPgType, nodeID, timestamp)
			if err != nil {
				a.logger.Error("Error inserting node creation log", "user_id", userID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		// Always log domain assignment (whether using existing node or new node)
		err = activitylogs.InsertLog(a.queries, "Domain assigned to engagement", db.LogsNodesTypeEnumDOMAINASSIGNED, *userIDPgType, nodeID, timestamp)
		if err != nil {
			a.logger.Error("Error inserting domain assignment log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		createdNodeIDString, err := converters.PgTypeUUIDToString(nodeID)
		if err != nil {
			a.logger.Error("Error converting Node ID to string", "user_id", userID, "error", err.Error(), "node_type", createdNode.NodeType, "node_id", createdNode.ID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		a.logger.Info("Created URL Node successfully",
			"user_id", userID,
			"engagement_id", i.Body.EngagementID,
			"url", i.Body.Url,
			"node_id", createdNodeIDString,
		)

		resp := &CreateNodeTypeUrlOutput{}
		resp.Body.NodeID = *createdNodeIDString
		resp.Body.EngagementID = i.Body.EngagementID
		resp.Body.Url = i.Body.Url

		return resp, nil
	})
}
